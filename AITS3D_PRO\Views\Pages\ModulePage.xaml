﻿<UserControl x:Class="AITS3D_PRO.Views.Pages.ModulePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AITS3D_PRO.Views.Pages"
             xmlns:Icon="http://metro.mahapps.com/winfx/xaml/iconpacks"
             xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:vm="clr-namespace:AITS3D_PRO.ViewModels.Pages;assembly=AITS3D_PRO"
             d:DesignHeight="450" d:DesignWidth="800">


    <DockPanel>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <!-- Toolbar -->
                <Border CornerRadius="15" 
                        BorderBrush="DarkGreen" 
                        BorderThickness="3" 
                        Margin="0" 
                        Padding="10,5">
                    <Border.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="Black"/>
                            <GradientStop Color="#FF345665" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    <StackPanel Orientation="Horizontal">
                        <Button Style="{StaticResource ToolbarButtonStyle}"
                                Command="{Binding SaveGraphCommand}"
                                ToolTip="Save Graph">
                            <StackPanel Orientation="Horizontal">
                                <Icon:PackIconMaterial Kind="ContentSave" Width="20" Height="20" Margin="0,0,5,0"/>
                                <TextBlock Text="Save"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ToolbarButtonStyle}"
                                Command="{Binding LoadGraphCommand}"
                                ToolTip="Load Graph">
                            <StackPanel Orientation="Horizontal">
                                <Icon:PackIconMaterial Kind="FolderOpen" Width="20" Height="20" Margin="0,0,5,0"/>
                                <TextBlock Text="Load"/>
                            </StackPanel>
                        </Button>

                        <Separator Margin="5,0"/>

                        <Button Style="{StaticResource ToolbarButtonStyle}"
                                Command="{Binding ExecuteGraphCommand}"
                                ToolTip="Execute Graph">
                            <StackPanel Orientation="Horizontal">
                                <Icon:PackIconMaterial Kind="Play" Width="20" Height="20" Margin="0,0,5,0"/>
                                <TextBlock Text="Execute"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ToolbarButtonStyle}"
                                Command="{Binding DeleteSelectedNodesCommand}"
                                ToolTip="Delete Selected Nodes">
                            <StackPanel Orientation="Horizontal">
                                <Icon:PackIconMaterial Kind="Delete" Width="20" Height="20" Margin="0,0,5,0"/>
                                <TextBlock Text="Delete"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ToolbarButtonStyle}"
                                Command="{Binding ClearGraphCommand}"
                                ToolTip="Clear Graph">
                            <StackPanel Orientation="Horizontal">
                                <Icon:PackIconMaterial Kind="DeleteSweep" Width="20" Height="20" Margin="0,0,5,0"/>
                                <TextBlock Text="Clear"/>
                            </StackPanel>
                        </Button>

                        <Separator Margin="5,0"/>

                        <TextBlock Text="Node-based Image Processing Editor"
                                   VerticalAlignment="Center"
                                   FontWeight="Bold"
                                   Foreground="LightBlue"
                                   Margin="10,0"/>
                    </StackPanel>
                </Border>
            </Grid>

            <Grid Grid.Row="1" Margin="0 5 0 0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="5*" />
                    <!-- 2/3 -->
                    <ColumnDefinition Width="*" />
                    <!-- 1/3 -->
                </Grid.ColumnDefinitions>

                <!-- Ô bên trái -->
                <Border Grid.Column="0" 
                        CornerRadius="15" 
                        BorderBrush="DarkGreen" 
                        BorderThickness="3" 
                        Margin="0">
                    <Border.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="Black"/>
                            <GradientStop Color="#FF345665" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>

                </Border>

                <!-- Ô bên phải -->
                <Border Grid.Column="1"
                        CornerRadius="15" 
                        BorderBrush="DarkGreen" 
                        BorderThickness="2" 
                        Margin="5 0 0 0">
                    <Border.Background>
                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                            <GradientStop Color="Black"/>
                            <GradientStop Color="#FF0A2835" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                </Border>
            </Grid>
        </Grid>
    </DockPanel>


</UserControl>
