{"version": 2, "dgSpecHash": "T6SyVvpl11k=", "success": true, "projectFilePath": "E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\AITS3D_PRO.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\controlzex\\4.3.0\\controlzex.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dynamicdata\\7.1.1\\dynamicdata.7.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\extended.wpf.toolkit\\5.0.0\\extended.wpf.toolkit.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\log4net\\2.0.12\\log4net.2.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro\\2.0.0\\mahapps.metro.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks\\6.1.0\\mahapps.metro.iconpacks.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.bootstrapicons\\6.1.0\\mahapps.metro.iconpacks.bootstrapicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.boxicons\\6.1.0\\mahapps.metro.iconpacks.boxicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.circumicons\\6.1.0\\mahapps.metro.iconpacks.circumicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.codicons\\6.1.0\\mahapps.metro.iconpacks.codicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.coolicons\\6.1.0\\mahapps.metro.iconpacks.coolicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.core\\6.1.0\\mahapps.metro.iconpacks.core.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.entypo\\6.1.0\\mahapps.metro.iconpacks.entypo.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.evaicons\\6.1.0\\mahapps.metro.iconpacks.evaicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.feathericons\\6.1.0\\mahapps.metro.iconpacks.feathericons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.fileicons\\6.1.0\\mahapps.metro.iconpacks.fileicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.fontaudio\\6.1.0\\mahapps.metro.iconpacks.fontaudio.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.fontawesome\\6.1.0\\mahapps.metro.iconpacks.fontawesome.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.fontawesome5\\6.1.0\\mahapps.metro.iconpacks.fontawesome5.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.fontawesome6\\6.1.0\\mahapps.metro.iconpacks.fontawesome6.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.fontisto\\6.1.0\\mahapps.metro.iconpacks.fontisto.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.forkawesome\\6.1.0\\mahapps.metro.iconpacks.forkawesome.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.gameicons\\6.1.0\\mahapps.metro.iconpacks.gameicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.ionicons\\6.1.0\\mahapps.metro.iconpacks.ionicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.jamicons\\6.1.0\\mahapps.metro.iconpacks.jamicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.keyruneicons\\6.1.0\\mahapps.metro.iconpacks.keyruneicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.lucide\\6.1.0\\mahapps.metro.iconpacks.lucide.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.material\\6.1.0\\mahapps.metro.iconpacks.material.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.materialdesign\\6.1.0\\mahapps.metro.iconpacks.materialdesign.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.materiallight\\6.1.0\\mahapps.metro.iconpacks.materiallight.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.memoryicons\\6.1.0\\mahapps.metro.iconpacks.memoryicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.microns\\6.1.0\\mahapps.metro.iconpacks.microns.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.mingcuteicons\\6.1.0\\mahapps.metro.iconpacks.mingcuteicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.modern\\6.1.0\\mahapps.metro.iconpacks.modern.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.mynauiicons\\6.1.0\\mahapps.metro.iconpacks.mynauiicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.octicons\\6.1.0\\mahapps.metro.iconpacks.octicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.phosphoricons\\6.1.0\\mahapps.metro.iconpacks.phosphoricons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.picolicons\\6.1.0\\mahapps.metro.iconpacks.picolicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.pixelarticons\\6.1.0\\mahapps.metro.iconpacks.pixelarticons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.radixicons\\6.1.0\\mahapps.metro.iconpacks.radixicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.remixicon\\6.1.0\\mahapps.metro.iconpacks.remixicon.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.rpgawesome\\6.1.0\\mahapps.metro.iconpacks.rpgawesome.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.simpleicons\\6.1.0\\mahapps.metro.iconpacks.simpleicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.typicons\\6.1.0\\mahapps.metro.iconpacks.typicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.unicons\\6.1.0\\mahapps.metro.iconpacks.unicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.vaadinicons\\6.1.0\\mahapps.metro.iconpacks.vaadinicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.weathericons\\6.1.0\\mahapps.metro.iconpacks.weathericons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro.iconpacks.zondicons\\6.1.0\\mahapps.metro.iconpacks.zondicons.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesigncolors\\5.2.1\\materialdesigncolors.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes\\5.2.1\\materialdesignthemes.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes.mahapps\\5.2.1\\materialdesignthemes.mahapps.5.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\2.0.0\\microsoft.netcore.platforms.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.39\\microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.4\\newtonsoft.json.13.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nodenetwork\\6.0.0\\nodenetwork.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pharmacist.common\\2.0.8\\pharmacist.common.2.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\reactiveui\\13.2.18\\reactiveui.13.2.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\reactiveui.events.wpf\\13.2.18\\reactiveui.events.wpf.13.2.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\reactiveui.wpf\\13.2.18\\reactiveui.wpf.13.2.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\splat\\11.0.1\\splat.11.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\splat.drawing\\11.0.1\\splat.drawing.11.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stringmath\\4.1.3\\stringmath.4.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\5.0.0\\system.collections.immutable.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\4.5.0\\system.configuration.configurationmanager.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.datasetextensions\\4.5.0\\system.data.datasetextensions.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.contracts\\4.3.0\\system.diagnostics.contracts.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.primitives\\4.3.0\\system.drawing.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reactive\\5.0.0\\system.reactive.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.5.0\\system.security.accesscontrol.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\4.5.0\\system.security.cryptography.protecteddata.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.5.0\\system.security.permissions.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.5.0\\system.security.principal.windows.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.5\\system.text.json.8.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512"], "logs": []}