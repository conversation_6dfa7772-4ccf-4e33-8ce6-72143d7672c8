{"version": 3, "targets": {"net8.0-windows7.0": {"ControlzEx/4.3.0": {"type": "package", "dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.19", "System.Text.Json": "4.7.2"}, "compile": {"lib/netcoreapp3.1/ControlzEx.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/ControlzEx.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "DynamicData/7.1.1": {"type": "package", "dependencies": {"System.Reactive": "5.0.0"}, "compile": {"lib/net5.0/DynamicData.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/DynamicData.dll": {"related": ".xml"}}}, "Extended.Wpf.Toolkit/5.0.0": {"type": "package", "compile": {"lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Aero.dll": {}, "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Metro.dll": {}, "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.VS2010.dll": {}, "lib/net5.0/Xceed.Wpf.AvalonDock.dll": {}, "lib/net5.0/Xceed.Wpf.Toolkit.dll": {}}, "runtime": {"lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Aero.dll": {}, "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Metro.dll": {}, "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.VS2010.dll": {}, "lib/net5.0/Xceed.Wpf.AvalonDock.dll": {}, "lib/net5.0/Xceed.Wpf.Toolkit.dll": {}}, "resource": {"lib/net5.0/cs-CZ/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "cs-CZ"}, "lib/net5.0/de/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "de"}, "lib/net5.0/es/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "es"}, "lib/net5.0/fr/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "fr"}, "lib/net5.0/hu/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "hu"}, "lib/net5.0/it/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "it"}, "lib/net5.0/ja-JP/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net5.0/pt-BR/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "pt-BR"}, "lib/net5.0/ro/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "ro"}, "lib/net5.0/ru/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "ru"}, "lib/net5.0/sv/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "sv"}, "lib/net5.0/zh-Hans/Xceed.Wpf.AvalonDock.resources.dll": {"locale": "zh-Hans"}}}, "log4net/2.0.12": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "4.5.0"}, "compile": {"lib/netstandard2.0/log4net.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/log4net.dll": {"related": ".xml"}}}, "MahApps.Metro/2.0.0": {"type": "package", "dependencies": {"ControlzEx": "4.3.0"}, "compile": {"lib/netcoreapp3.1/MahApps.Metro.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/MahApps.Metro.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.BootstrapIcons": "6.1.0", "MahApps.Metro.IconPacks.BoxIcons": "6.1.0", "MahApps.Metro.IconPacks.CircumIcons": "6.1.0", "MahApps.Metro.IconPacks.Codicons": "6.1.0", "MahApps.Metro.IconPacks.Coolicons": "6.1.0", "MahApps.Metro.IconPacks.Core": "6.1.0", "MahApps.Metro.IconPacks.Entypo": "6.1.0", "MahApps.Metro.IconPacks.EvaIcons": "6.1.0", "MahApps.Metro.IconPacks.FeatherIcons": "6.1.0", "MahApps.Metro.IconPacks.FileIcons": "6.1.0", "MahApps.Metro.IconPacks.FontAwesome": "6.1.0", "MahApps.Metro.IconPacks.FontAwesome5": "6.1.0", "MahApps.Metro.IconPacks.FontAwesome6": "6.1.0", "MahApps.Metro.IconPacks.Fontaudio": "6.1.0", "MahApps.Metro.IconPacks.Fontisto": "6.1.0", "MahApps.Metro.IconPacks.ForkAwesome": "6.1.0", "MahApps.Metro.IconPacks.GameIcons": "6.1.0", "MahApps.Metro.IconPacks.Ionicons": "6.1.0", "MahApps.Metro.IconPacks.JamIcons": "6.1.0", "MahApps.Metro.IconPacks.KeyruneIcons": "6.1.0", "MahApps.Metro.IconPacks.Lucide": "6.1.0", "MahApps.Metro.IconPacks.Material": "6.1.0", "MahApps.Metro.IconPacks.MaterialDesign": "6.1.0", "MahApps.Metro.IconPacks.MaterialLight": "6.1.0", "MahApps.Metro.IconPacks.MemoryIcons": "6.1.0", "MahApps.Metro.IconPacks.Microns": "6.1.0", "MahApps.Metro.IconPacks.MingCuteIcons": "6.1.0", "MahApps.Metro.IconPacks.Modern": "6.1.0", "MahApps.Metro.IconPacks.MynaUIIcons": "6.1.0", "MahApps.Metro.IconPacks.Octicons": "6.1.0", "MahApps.Metro.IconPacks.PhosphorIcons": "6.1.0", "MahApps.Metro.IconPacks.PicolIcons": "6.1.0", "MahApps.Metro.IconPacks.PixelartIcons": "6.1.0", "MahApps.Metro.IconPacks.RPGAwesome": "6.1.0", "MahApps.Metro.IconPacks.RadixIcons": "6.1.0", "MahApps.Metro.IconPacks.RemixIcon": "6.1.0", "MahApps.Metro.IconPacks.SimpleIcons": "6.1.0", "MahApps.Metro.IconPacks.Typicons": "6.1.0", "MahApps.Metro.IconPacks.Unicons": "6.1.0", "MahApps.Metro.IconPacks.VaadinIcons": "6.1.0", "MahApps.Metro.IconPacks.WeatherIcons": "6.1.0", "MahApps.Metro.IconPacks.Zondicons": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.BootstrapIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BootstrapIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BootstrapIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.BoxIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BoxIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BoxIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.CircumIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.CircumIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.CircumIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Codicons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Codicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Codicons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Coolicons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Coolicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Coolicons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Core/6.1.0": {"type": "package", "dependencies": {"System.Text.Json": "8.0.5"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Core.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Entypo/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Entypo.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Entypo.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.EvaIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.EvaIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.EvaIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.FeatherIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FeatherIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FeatherIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.FileIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FileIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FileIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Fontaudio/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontaudio.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontaudio.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.FontAwesome/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.FontAwesome5/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome5.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome5.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.FontAwesome6/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome6.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome6.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Fontisto/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontisto.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontisto.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.ForkAwesome/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.ForkAwesome.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.ForkAwesome.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.GameIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.GameIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.GameIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Ionicons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Ionicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Ionicons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.JamIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.JamIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.JamIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.KeyruneIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.KeyruneIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.KeyruneIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Lucide/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Lucide.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Lucide.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Material/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Material.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Material.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.MaterialDesign/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialDesign.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialDesign.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.MaterialLight/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialLight.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialLight.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.MemoryIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MemoryIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MemoryIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Microns/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Microns.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Microns.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.MingCuteIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MingCuteIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MingCuteIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Modern/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Modern.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Modern.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.MynaUIIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MynaUIIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MynaUIIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Octicons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Octicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Octicons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.PhosphorIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PhosphorIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PhosphorIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.PicolIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PicolIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PicolIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.PixelartIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PixelartIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PixelartIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.RadixIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RadixIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RadixIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.RemixIcon/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RemixIcon.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RemixIcon.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.RPGAwesome/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RPGAwesome.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RPGAwesome.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.SimpleIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.SimpleIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.SimpleIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Typicons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Typicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Typicons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Unicons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Unicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Unicons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.VaadinIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.VaadinIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.VaadinIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.WeatherIcons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.WeatherIcons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.WeatherIcons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MahApps.Metro.IconPacks.Zondicons/6.1.0": {"type": "package", "dependencies": {"MahApps.Metro.IconPacks.Core": "6.1.0"}, "compile": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Zondicons.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Zondicons.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MaterialDesignColors/5.2.1": {"type": "package", "compile": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/5.2.1": {"type": "package", "dependencies": {"MaterialDesignColors": "5.2.1", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "MaterialDesignThemes.MahApps/5.2.1": {"type": "package", "dependencies": {"MahApps.Metro": "2.0.0", "MaterialDesignColors": "5.2.1", "MaterialDesignThemes": "5.2.1"}, "compile": {"lib/net8.0/MaterialDesignThemes.MahApps.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/MaterialDesignThemes.MahApps.dll": {"related": ".pdb"}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.NETCore.Platforms/2.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "NodeNetwork/6.0.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "ReactiveUI": "13.2.18", "ReactiveUI.Events.WPF": "13.2.18", "ReactiveUI.WPF": "13.2.18", "Splat.Drawing": "11.0.1", "System.Buffers": "4.5.1", "System.Collections.Immutable": "5.0.0", "System.Data.DataSetExtensions": "4.5.0", "System.Drawing.Primitives": "4.3.0", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0", "log4net": "2.0.12"}, "compile": {"lib/netcoreapp3.1/NodeNetwork.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/NodeNetwork.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App", "Microsoft.WindowsDesktop.App.WPF"]}, "Pharmacist.Common/2.0.8": {"type": "package", "dependencies": {"System.Reactive": "5.0.0"}, "compile": {"lib/net5.0/Pharmacist.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Pharmacist.Common.dll": {"related": ".xml"}}}, "ReactiveUI/13.2.18": {"type": "package", "dependencies": {"DynamicData": "7.1.1", "Splat": "11.0.1", "System.Reactive": "5.0.0", "System.Runtime.Serialization.Primitives": "4.3.0"}, "compile": {"lib/net5.0-windows7.0/ReactiveUI.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0-windows7.0/ReactiveUI.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}, "ReactiveUI.Events.WPF/13.2.18": {"type": "package", "dependencies": {"Pharmacist.Common": "2.0.8", "System.Reactive": "5.0.0"}, "compile": {"lib/net5.0-windows7.0/ReactiveUI.Events.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0-windows7.0/ReactiveUI.Events.WPF.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}, "ReactiveUI.WPF/13.2.18": {"type": "package", "dependencies": {"ReactiveUI": "13.2.18", "System.Reactive": "5.0.0"}, "compile": {"lib/net5.0-windows7.0/ReactiveUI.Wpf.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0-windows7.0/ReactiveUI.Wpf.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Splat/11.0.1": {"type": "package", "compile": {"lib/net5.0/Splat.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Splat.dll": {"related": ".xml"}}}, "Splat.Drawing/11.0.1": {"type": "package", "dependencies": {"Splat": "11.0.1", "System.Diagnostics.Contracts": "4.3.0", "System.Drawing.Primitives": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0"}, "compile": {"lib/net5.0-windows7.0/Splat.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0-windows7.0/Splat.Drawing.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}, "StringMath/4.1.3": {"type": "package", "compile": {"lib/net8.0/StringMath.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/StringMath.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections.Immutable/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/4.5.0": {"type": "package", "dependencies": {"System.Security.Cryptography.ProtectedData": "4.5.0", "System.Security.Permissions": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Data.DataSetExtensions.dll": {}}, "runtime": {"lib/netstandard2.0/System.Data.DataSetExtensions.dll": {}}}, "System.Diagnostics.Contracts/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Diagnostics.Contracts.dll": {}}}, "System.Drawing.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Drawing.Primitives.dll": {}}, "runtime": {"lib/netstandard1.1/System.Drawing.Primitives.dll": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reactive/5.0.0": {"type": "package", "compile": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Security.AccessControl/4.5.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Security.Principal.Windows": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/4.5.0": {"type": "package", "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/4.5.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.5.0"}, "compile": {"ref/netstandard2.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Permissions.dll": {}}}, "System.Security.Principal.Windows/4.5.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0"}, "compile": {"ref/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Text.Json/8.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}}}, "libraries": {"ControlzEx/4.3.0": {"sha512": "I7PKmQNIMO9+s4s50WXOApotnyEKL9G8vvYrZY7CLe7nufXwRSv+vWPrd4BYwZDQiWHbBwnXsC8zUPgPnEWr6g==", "type": "package", "path": "controlzex/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "controlzex.4.3.0.nupkg.sha512", "controlzex.nuspec", "lib/net45/ControlzEx.dll", "lib/net45/ControlzEx.pdb", "lib/net45/ControlzEx.xml", "lib/net462/ControlzEx.dll", "lib/net462/ControlzEx.pdb", "lib/net462/ControlzEx.xml", "lib/netcoreapp3.0/ControlzEx.dll", "lib/netcoreapp3.0/ControlzEx.pdb", "lib/netcoreapp3.0/ControlzEx.xml", "lib/netcoreapp3.1/ControlzEx.dll", "lib/netcoreapp3.1/ControlzEx.pdb", "lib/netcoreapp3.1/ControlzEx.xml", "logo-mini.png"]}, "DynamicData/7.1.1": {"sha512": "Pc6J5bFnSxEa64PV2V67FMcLlDdpv6m+zTBKSnRN3aLon/WtWWy8kuDpHFbJlgXHtqc6Nxloj9ItuvDlvKC/8w==", "type": "package", "path": "dynamicdata/7.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "dynamicdata.7.1.1.nupkg.sha512", "dynamicdata.nuspec", "lib/net461/DynamicData.dll", "lib/net461/DynamicData.xml", "lib/net5.0/DynamicData.dll", "lib/net5.0/DynamicData.xml", "lib/netstandard2.0/DynamicData.dll", "lib/netstandard2.0/DynamicData.xml", "lib/uap10.0.16299/DynamicData.dll", "lib/uap10.0.16299/DynamicData.pri", "lib/uap10.0.16299/DynamicData.xml"]}, "Extended.Wpf.Toolkit/5.0.0": {"sha512": "8QStq5cQd+anGBbA5XXSlb/BxEk457xRQ/vraLXNvf5fHG1b4Bu4DOkvSqvvu9MLRUf+q29ceWsLRGtAw3CzDg==", "type": "package", "path": "extended.wpf.toolkit/5.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "Xceed_Logo_Retina-400x400.png", "extended.wpf.toolkit.5.0.0.nupkg.sha512", "extended.wpf.toolkit.nuspec", "lib/net40/Xceed.Wpf.AvalonDock.Themes.Aero.dll", "lib/net40/Xceed.Wpf.AvalonDock.Themes.Metro.dll", "lib/net40/Xceed.Wpf.AvalonDock.Themes.VS2010.dll", "lib/net40/Xceed.Wpf.AvalonDock.dll", "lib/net40/Xceed.Wpf.Toolkit.dll", "lib/net40/cs-CZ/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/de/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/es/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/fr/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/hu/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/it/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/ja-JP/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/nl-BE/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/pt-BR/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/ro/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/ru/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/sv/Xceed.Wpf.AvalonDock.resources.dll", "lib/net40/zh-Hans/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Aero.dll", "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.Metro.dll", "lib/net5.0/Xceed.Wpf.AvalonDock.Themes.VS2010.dll", "lib/net5.0/Xceed.Wpf.AvalonDock.dll", "lib/net5.0/Xceed.Wpf.Toolkit.dll", "lib/net5.0/cs-CZ/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/de/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/es/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/fr/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/hu/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/it/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/ja-JP/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/pt-BR/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/ro/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/ru/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/sv/Xceed.Wpf.AvalonDock.resources.dll", "lib/net5.0/zh-Hans/Xceed.Wpf.AvalonDock.resources.dll", "tools/install.ps1"]}, "log4net/2.0.12": {"sha512": "9P67BCftJ7KG+B7rNOM1A9KczUwyEDed6zbAddy5Cj/73xVkzi+rEAHeOgUnW5wDqy1JFlY8+oTP0m1PgJ03Tg==", "type": "package", "path": "log4net/2.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/log4net.dll", "lib/net20/log4net.xml", "lib/net35-client/log4net.dll", "lib/net35-client/log4net.xml", "lib/net35/log4net.dll", "lib/net35/log4net.xml", "lib/net40-client/log4net.dll", "lib/net40-client/log4net.xml", "lib/net40/log4net.dll", "lib/net40/log4net.xml", "lib/net45/log4net.dll", "lib/net45/log4net.xml", "lib/netstandard1.3/log4net.dll", "lib/netstandard1.3/log4net.xml", "lib/netstandard2.0/log4net.dll", "lib/netstandard2.0/log4net.xml", "log4net.2.0.12.nupkg.sha512", "log4net.nuspec", "package-icon.png"]}, "MahApps.Metro/2.0.0": {"sha512": "btjlE3Q8iebJB+4TBJZLcJBQZshKYqfkcATj9AS8veq4a506uE71NOTE1OSoavOJ70xasBjcdc+VuN17l+kIqw==", "type": "package", "path": "mahapps.metro/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/MahApps.Metro.dll", "lib/net452/MahApps.Metro.pdb", "lib/net452/MahApps.Metro.xml", "lib/net46/MahApps.Metro.dll", "lib/net46/MahApps.Metro.pdb", "lib/net46/MahApps.Metro.xml", "lib/net47/MahApps.Metro.dll", "lib/net47/MahApps.Metro.pdb", "lib/net47/MahApps.Metro.xml", "lib/netcoreapp3.0/MahApps.Metro.dll", "lib/netcoreapp3.0/MahApps.Metro.pdb", "lib/netcoreapp3.0/MahApps.Metro.xml", "lib/netcoreapp3.1/MahApps.Metro.dll", "lib/netcoreapp3.1/MahApps.Metro.pdb", "lib/netcoreapp3.1/MahApps.Metro.xml", "mahapps.metro.2.0.0.nupkg.sha512", "mahapps.metro.logo.png", "mahapps.metro.nuspec"]}, "MahApps.Metro.IconPacks/6.1.0": {"sha512": "z9ICQimvka9qfz1uo5T/H2TzdOw5V5PPBXqgiHJ5kw01yFY5jTqoW1bCrs24Gz3gM5eXZa1FhmiIuC9Ii8TWkQ==", "type": "package", "path": "mahapps.metro.iconpacks/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.dll", "lib/net462/MahApps.Metro.IconPacks.xml", "lib/net47/MahApps.Metro.IconPacks.dll", "lib/net47/MahApps.Metro.IconPacks.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.xml", "logo_small.png", "mahapps.metro.iconpacks.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.nuspec"]}, "MahApps.Metro.IconPacks.BootstrapIcons/6.1.0": {"sha512": "+b9Hngiuk8K2QZAeCb7fSAKFSMtP/lKlZPLZWpF7Wm9SL8FNG3z3fUr0u+wP8jwZYDpn/ycQyz0ueXgiqyD2uQ==", "type": "package", "path": "mahapps.metro.iconpacks.bootstrapicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.BootstrapIcons.dll", "lib/net462/MahApps.Metro.IconPacks.BootstrapIcons.xml", "lib/net47/MahApps.Metro.IconPacks.BootstrapIcons.dll", "lib/net47/MahApps.Metro.IconPacks.BootstrapIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.BootstrapIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.BootstrapIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BootstrapIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BootstrapIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.bootstrapicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.bootstrapicons.nuspec"]}, "MahApps.Metro.IconPacks.BoxIcons/6.1.0": {"sha512": "cK/I3XsgrGBeimFNt2lSmNwx+NWPKM+qZkgd8o6QR+ZDcFZtjdalp0MtGaxCCrN7RvsyshnlbkHvzaGzLo7gUg==", "type": "package", "path": "mahapps.metro.iconpacks.boxicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.BoxIcons.dll", "lib/net462/MahApps.Metro.IconPacks.BoxIcons.xml", "lib/net47/MahApps.Metro.IconPacks.BoxIcons.dll", "lib/net47/MahApps.Metro.IconPacks.BoxIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.BoxIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.BoxIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BoxIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.BoxIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.boxicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.boxicons.nuspec"]}, "MahApps.Metro.IconPacks.CircumIcons/6.1.0": {"sha512": "KVg+IaPqlEff+lLtfVnaf7KpABab+fFbM0TgyqlOV1SX3YtOdj4GS1yTu1hYjgGUksjNu7WXvYUh9ScE6/pPyA==", "type": "package", "path": "mahapps.metro.iconpacks.circumicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.CircumIcons.dll", "lib/net462/MahApps.Metro.IconPacks.CircumIcons.xml", "lib/net47/MahApps.Metro.IconPacks.CircumIcons.dll", "lib/net47/MahApps.Metro.IconPacks.CircumIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.CircumIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.CircumIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.CircumIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.CircumIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.circumicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.circumicons.nuspec"]}, "MahApps.Metro.IconPacks.Codicons/6.1.0": {"sha512": "bax2Tz9l9mq9cMZVm7p4y2yd3Ulug8TT0YKtJ+aIB64LCq54NtElqBlbiK2tFuS8hb12zhvm8TzAFVw+glFdXA==", "type": "package", "path": "mahapps.metro.iconpacks.codicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Codicons.dll", "lib/net462/MahApps.Metro.IconPacks.Codicons.xml", "lib/net47/MahApps.Metro.IconPacks.Codicons.dll", "lib/net47/MahApps.Metro.IconPacks.Codicons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Codicons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Codicons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Codicons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Codicons.xml", "logo_small.png", "mahapps.metro.iconpacks.codicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.codicons.nuspec"]}, "MahApps.Metro.IconPacks.Coolicons/6.1.0": {"sha512": "UKsVnFUZ4oi/OvSSQ6sShMhJLgof/HpaD7CMhCk4TMLh1UnLUKkOcXwrk5YwrtK8EHgudYwZQicNS8wBUckCYw==", "type": "package", "path": "mahapps.metro.iconpacks.coolicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Coolicons.dll", "lib/net462/MahApps.Metro.IconPacks.Coolicons.xml", "lib/net47/MahApps.Metro.IconPacks.Coolicons.dll", "lib/net47/MahApps.Metro.IconPacks.Coolicons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Coolicons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Coolicons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Coolicons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Coolicons.xml", "logo_small.png", "mahapps.metro.iconpacks.coolicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.coolicons.nuspec"]}, "MahApps.Metro.IconPacks.Core/6.1.0": {"sha512": "op3E+N8dKBRiKwqy7/P9d9vG/1+HcaUohEsEUkN8FtAmQEEpmAo88Y6GIIqsmtheXsks4VGAVtXgzsfgxL12eA==", "type": "package", "path": "mahapps.metro.iconpacks.core/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Core.dll", "lib/net462/MahApps.Metro.IconPacks.Core.xml", "lib/net47/MahApps.Metro.IconPacks.Core.dll", "lib/net47/MahApps.Metro.IconPacks.Core.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Core.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Core.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Core.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Core.xml", "logo_small.png", "mahapps.metro.iconpacks.core.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.core.nuspec"]}, "MahApps.Metro.IconPacks.Entypo/6.1.0": {"sha512": "K2S+kFheCcGjVP4e62ROAq9VBjXSD3wneQIFWtCGDygGayv5+1glr0mOp9OCX9e2aLY3+it5TFHSa64N1gqOxw==", "type": "package", "path": "mahapps.metro.iconpacks.entypo/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Entypo.dll", "lib/net462/MahApps.Metro.IconPacks.Entypo.xml", "lib/net47/MahApps.Metro.IconPacks.Entypo.dll", "lib/net47/MahApps.Metro.IconPacks.Entypo.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Entypo.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Entypo.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Entypo.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Entypo.xml", "logo_small.png", "mahapps.metro.iconpacks.entypo.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.entypo.nuspec"]}, "MahApps.Metro.IconPacks.EvaIcons/6.1.0": {"sha512": "omWdvMKrCt1bsSr4EZdnXbr6bqReKtpdCc78lTDAxi+B5xzU7SVtTwnH/VQ2Dv8AjgT0SZdKFgnRE9G1azsNtw==", "type": "package", "path": "mahapps.metro.iconpacks.evaicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.EvaIcons.dll", "lib/net462/MahApps.Metro.IconPacks.EvaIcons.xml", "lib/net47/MahApps.Metro.IconPacks.EvaIcons.dll", "lib/net47/MahApps.Metro.IconPacks.EvaIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.EvaIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.EvaIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.EvaIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.EvaIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.evaicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.evaicons.nuspec"]}, "MahApps.Metro.IconPacks.FeatherIcons/6.1.0": {"sha512": "Mwhf6yhCisuks6kl8mcKDngdjGlDE5K78fMxvqkbieGo0bupBbDK+0O7j8AVkGTPvkT1Zlik5Kh/+VybWA3brg==", "type": "package", "path": "mahapps.metro.iconpacks.feathericons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.FeatherIcons.dll", "lib/net462/MahApps.Metro.IconPacks.FeatherIcons.xml", "lib/net47/MahApps.Metro.IconPacks.FeatherIcons.dll", "lib/net47/MahApps.Metro.IconPacks.FeatherIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FeatherIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FeatherIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FeatherIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FeatherIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.feathericons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.feathericons.nuspec"]}, "MahApps.Metro.IconPacks.FileIcons/6.1.0": {"sha512": "FvUuYJmL/dgt5ArtGtyyXPIRJCB1FHvq4DYlQ4p4xYl91jYqRyNIr23iVhgRg7e9F99/HKrAhBD1gpSHGjHY4w==", "type": "package", "path": "mahapps.metro.iconpacks.fileicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.FileIcons.dll", "lib/net462/MahApps.Metro.IconPacks.FileIcons.xml", "lib/net47/MahApps.Metro.IconPacks.FileIcons.dll", "lib/net47/MahApps.Metro.IconPacks.FileIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FileIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FileIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FileIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FileIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.fileicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.fileicons.nuspec"]}, "MahApps.Metro.IconPacks.Fontaudio/6.1.0": {"sha512": "GEANxhfUZH9TFAeiVkcBK/3RDcIyXz6d7moz8dQc2zjE4yqtzuENAoSOeguM7d1wOHX8phkexb1C6XqWqQVmcQ==", "type": "package", "path": "mahapps.metro.iconpacks.fontaudio/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Fontaudio.dll", "lib/net462/MahApps.Metro.IconPacks.Fontaudio.xml", "lib/net47/MahApps.Metro.IconPacks.Fontaudio.dll", "lib/net47/MahApps.Metro.IconPacks.Fontaudio.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Fontaudio.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Fontaudio.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontaudio.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontaudio.xml", "logo_small.png", "mahapps.metro.iconpacks.fontaudio.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.fontaudio.nuspec"]}, "MahApps.Metro.IconPacks.FontAwesome/6.1.0": {"sha512": "xuWDxbsIMZTqTPt5dlf8i5NZNsLXzXjLLkdVy08XwxmhoTdXRWK3e/mQTsjPP+k6P8kOieSlhufZ1ssw3KP54A==", "type": "package", "path": "mahapps.metro.iconpacks.fontawesome/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.FontAwesome.dll", "lib/net462/MahApps.Metro.IconPacks.FontAwesome.xml", "lib/net47/MahApps.Metro.IconPacks.FontAwesome.dll", "lib/net47/MahApps.Metro.IconPacks.FontAwesome.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome.xml", "logo_small.png", "mahapps.metro.iconpacks.fontawesome.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.fontawesome.nuspec"]}, "MahApps.Metro.IconPacks.FontAwesome5/6.1.0": {"sha512": "WnfKiEX37uhs+I9YEReDlP+giyjEyXzBunRP383F+82OXlGIBiLADHWVqGTFVJzmDF7BXkym0OLU3MoC/RAThg==", "type": "package", "path": "mahapps.metro.iconpacks.fontawesome5/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.FontAwesome5.dll", "lib/net462/MahApps.Metro.IconPacks.FontAwesome5.xml", "lib/net47/MahApps.Metro.IconPacks.FontAwesome5.dll", "lib/net47/MahApps.Metro.IconPacks.FontAwesome5.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome5.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome5.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome5.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome5.xml", "logo_small.png", "mahapps.metro.iconpacks.fontawesome5.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.fontawesome5.nuspec"]}, "MahApps.Metro.IconPacks.FontAwesome6/6.1.0": {"sha512": "TNKFewuanGB0PTFZ7x3+zQml8lt91+HeEjfKxrMB2CEo+cS7RffFwnTVF5KXBIMW8JQZsc0L7Vv5xk34L2MMSg==", "type": "package", "path": "mahapps.metro.iconpacks.fontawesome6/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.FontAwesome6.dll", "lib/net462/MahApps.Metro.IconPacks.FontAwesome6.xml", "lib/net47/MahApps.Metro.IconPacks.FontAwesome6.dll", "lib/net47/MahApps.Metro.IconPacks.FontAwesome6.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome6.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome6.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome6.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.FontAwesome6.xml", "logo_small.png", "mahapps.metro.iconpacks.fontawesome6.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.fontawesome6.nuspec"]}, "MahApps.Metro.IconPacks.Fontisto/6.1.0": {"sha512": "8QWc3eELx0LpMf9VMeszVxOAUa1wCvfk6Am5pOAPPgT6Cd093YeEQvW0AsRNPELe5IbjDiwTPXTnZjDIEykQWw==", "type": "package", "path": "mahapps.metro.iconpacks.fontisto/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Fontisto.dll", "lib/net462/MahApps.Metro.IconPacks.Fontisto.xml", "lib/net47/MahApps.Metro.IconPacks.Fontisto.dll", "lib/net47/MahApps.Metro.IconPacks.Fontisto.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Fontisto.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Fontisto.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontisto.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Fontisto.xml", "logo_small.png", "mahapps.metro.iconpacks.fontisto.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.fontisto.nuspec"]}, "MahApps.Metro.IconPacks.ForkAwesome/6.1.0": {"sha512": "zKWAIjq1KwXGTPBMqy4nJF4L/BXgvaruyxIFC4eBY9foJ1XnNvzp+8mD38zdw7pEfJm53SU/3g+JIbg+DRL19g==", "type": "package", "path": "mahapps.metro.iconpacks.forkawesome/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.ForkAwesome.dll", "lib/net462/MahApps.Metro.IconPacks.ForkAwesome.xml", "lib/net47/MahApps.Metro.IconPacks.ForkAwesome.dll", "lib/net47/MahApps.Metro.IconPacks.ForkAwesome.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.ForkAwesome.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.ForkAwesome.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.ForkAwesome.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.ForkAwesome.xml", "logo_small.png", "mahapps.metro.iconpacks.forkawesome.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.forkawesome.nuspec"]}, "MahApps.Metro.IconPacks.GameIcons/6.1.0": {"sha512": "P1opkobttg1cox2kBXXhC1Me3ppYwFCBI7poByYC+WtSd8ZDQuTy2gk2rim0TIszTAH2jByfORT5kvqqwXDrtA==", "type": "package", "path": "mahapps.metro.iconpacks.gameicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.GameIcons.dll", "lib/net462/MahApps.Metro.IconPacks.GameIcons.xml", "lib/net47/MahApps.Metro.IconPacks.GameIcons.dll", "lib/net47/MahApps.Metro.IconPacks.GameIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.GameIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.GameIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.GameIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.GameIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.gameicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.gameicons.nuspec"]}, "MahApps.Metro.IconPacks.Ionicons/6.1.0": {"sha512": "2hGZx4qnCyB/GoMMcFm+pj5zZUpe7AysexC+TtKRyAdsThRzYkX6nvl4RGbmhCDbo59P0VjKhDxfc6uQjwBabA==", "type": "package", "path": "mahapps.metro.iconpacks.ionicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Ionicons.dll", "lib/net462/MahApps.Metro.IconPacks.Ionicons.xml", "lib/net47/MahApps.Metro.IconPacks.Ionicons.dll", "lib/net47/MahApps.Metro.IconPacks.Ionicons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Ionicons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Ionicons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Ionicons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Ionicons.xml", "logo_small.png", "mahapps.metro.iconpacks.ionicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.ionicons.nuspec"]}, "MahApps.Metro.IconPacks.JamIcons/6.1.0": {"sha512": "4uaLee73FGyLNGRMJG3blYcLadfJ7Z9HDcJAkUehdvwo2OYsGnTyC0ZFQ+CW+ocguBCU8xgEwO60oTOIN6L4Pw==", "type": "package", "path": "mahapps.metro.iconpacks.jamicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.JamIcons.dll", "lib/net462/MahApps.Metro.IconPacks.JamIcons.xml", "lib/net47/MahApps.Metro.IconPacks.JamIcons.dll", "lib/net47/MahApps.Metro.IconPacks.JamIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.JamIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.JamIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.JamIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.JamIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.jamicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.jamicons.nuspec"]}, "MahApps.Metro.IconPacks.KeyruneIcons/6.1.0": {"sha512": "z7XS712WiW+W8Q0nhGrJGiE/dMIiaQnfnAL5oeRbgPHkyH6GyQposQruTjqUDByF0rA3SF6X7L/tHFytwowePw==", "type": "package", "path": "mahapps.metro.iconpacks.keyruneicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.KeyruneIcons.dll", "lib/net462/MahApps.Metro.IconPacks.KeyruneIcons.xml", "lib/net47/MahApps.Metro.IconPacks.KeyruneIcons.dll", "lib/net47/MahApps.Metro.IconPacks.KeyruneIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.KeyruneIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.KeyruneIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.KeyruneIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.KeyruneIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.keyruneicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.keyruneicons.nuspec"]}, "MahApps.Metro.IconPacks.Lucide/6.1.0": {"sha512": "vqcuLBZ6rN7noT+C3vRMWWdumG1lZ8e9cDJTj92uJcnzo4rSq+zZzcXPFwRqIBKS8+s6KtUFih9AJ1YsfvgEwQ==", "type": "package", "path": "mahapps.metro.iconpacks.lucide/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Lucide.dll", "lib/net462/MahApps.Metro.IconPacks.Lucide.xml", "lib/net47/MahApps.Metro.IconPacks.Lucide.dll", "lib/net47/MahApps.Metro.IconPacks.Lucide.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Lucide.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Lucide.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Lucide.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Lucide.xml", "logo_small.png", "mahapps.metro.iconpacks.lucide.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.lucide.nuspec"]}, "MahApps.Metro.IconPacks.Material/6.1.0": {"sha512": "PGTlI0v1RfA9CZPAafKtHAhXR6W2UgVFWDsInyOuLF1jMnv8YccE240ZSe02a+xsAwnAhtROAPRfagLkJAIQcQ==", "type": "package", "path": "mahapps.metro.iconpacks.material/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Material.dll", "lib/net462/MahApps.Metro.IconPacks.Material.xml", "lib/net47/MahApps.Metro.IconPacks.Material.dll", "lib/net47/MahApps.Metro.IconPacks.Material.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Material.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Material.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Material.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Material.xml", "logo_small.png", "mahapps.metro.iconpacks.material.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.material.nuspec"]}, "MahApps.Metro.IconPacks.MaterialDesign/6.1.0": {"sha512": "TUZELfw6oVYooq5bTX2Ecj4APW+6FRofKvvNDa0i274SBtV5FY7dlLognLHBhqoxDDLOpQPeCErPfZZDFjyH8w==", "type": "package", "path": "mahapps.metro.iconpacks.materialdesign/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.MaterialDesign.dll", "lib/net462/MahApps.Metro.IconPacks.MaterialDesign.xml", "lib/net47/MahApps.Metro.IconPacks.MaterialDesign.dll", "lib/net47/MahApps.Metro.IconPacks.MaterialDesign.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MaterialDesign.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MaterialDesign.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialDesign.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialDesign.xml", "logo_small.png", "mahapps.metro.iconpacks.materialdesign.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.materialdesign.nuspec"]}, "MahApps.Metro.IconPacks.MaterialLight/6.1.0": {"sha512": "PF3jIEgcHZxNjcB+kwmUAFDk/tFdMF92L0819qDus0hfg4K8fW0VENxd1HuTg72XRmwO/rvw34F8LDSCRBOQ8Q==", "type": "package", "path": "mahapps.metro.iconpacks.materiallight/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.MaterialLight.dll", "lib/net462/MahApps.Metro.IconPacks.MaterialLight.xml", "lib/net47/MahApps.Metro.IconPacks.MaterialLight.dll", "lib/net47/MahApps.Metro.IconPacks.MaterialLight.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MaterialLight.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MaterialLight.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialLight.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MaterialLight.xml", "logo_small.png", "mahapps.metro.iconpacks.materiallight.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.materiallight.nuspec"]}, "MahApps.Metro.IconPacks.MemoryIcons/6.1.0": {"sha512": "sp0WUUecC4BroDsqcc7STg88bUWMeShc2UyTPM1MM/NctCpN1V0A8Qn0EdtBJ3JTva0T7ObGVs9BGd5SMjMxpw==", "type": "package", "path": "mahapps.metro.iconpacks.memoryicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.MemoryIcons.dll", "lib/net462/MahApps.Metro.IconPacks.MemoryIcons.xml", "lib/net47/MahApps.Metro.IconPacks.MemoryIcons.dll", "lib/net47/MahApps.Metro.IconPacks.MemoryIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MemoryIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MemoryIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MemoryIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MemoryIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.memoryicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.memoryicons.nuspec"]}, "MahApps.Metro.IconPacks.Microns/6.1.0": {"sha512": "Szcg0+6aMeIiVKkmX9LhNEJ2gREZlXZ5qabQMNTCg8ZxUfn/a+hTGbvs/2gT6SKaWpf9+dB1PiMCkk+H3wS27g==", "type": "package", "path": "mahapps.metro.iconpacks.microns/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Microns.dll", "lib/net462/MahApps.Metro.IconPacks.Microns.xml", "lib/net47/MahApps.Metro.IconPacks.Microns.dll", "lib/net47/MahApps.Metro.IconPacks.Microns.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Microns.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Microns.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Microns.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Microns.xml", "logo_small.png", "mahapps.metro.iconpacks.microns.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.microns.nuspec"]}, "MahApps.Metro.IconPacks.MingCuteIcons/6.1.0": {"sha512": "pBsoexyTfdyi2lIYKzZzsFgaDrOoS285kHkX3OGdrIWu1f21BotILW62hfc56P8oUd8qKHsHD88ForwjJn6VxQ==", "type": "package", "path": "mahapps.metro.iconpacks.mingcuteicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.MingCuteIcons.dll", "lib/net462/MahApps.Metro.IconPacks.MingCuteIcons.xml", "lib/net47/MahApps.Metro.IconPacks.MingCuteIcons.dll", "lib/net47/MahApps.Metro.IconPacks.MingCuteIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MingCuteIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MingCuteIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MingCuteIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MingCuteIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.mingcuteicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.mingcuteicons.nuspec"]}, "MahApps.Metro.IconPacks.Modern/6.1.0": {"sha512": "Pl/S5z/1H5dvZYAGuEexZWapr7wHfMzSwPvkBWrYbzOc2zi+VsNZTx44IuKLhSmpu/v8G8iXdocu1pfqcnyyTw==", "type": "package", "path": "mahapps.metro.iconpacks.modern/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Modern.dll", "lib/net462/MahApps.Metro.IconPacks.Modern.xml", "lib/net47/MahApps.Metro.IconPacks.Modern.dll", "lib/net47/MahApps.Metro.IconPacks.Modern.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Modern.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Modern.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Modern.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Modern.xml", "logo_small.png", "mahapps.metro.iconpacks.modern.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.modern.nuspec"]}, "MahApps.Metro.IconPacks.MynaUIIcons/6.1.0": {"sha512": "IMT/d1mctrVt+KYPdTqrcsZ8uQ3kqJ1Rj/y+1sdzBR5HFfp76J/mhKhDpgJZXCcAbS8wl6utb+PS/2evXhmU4w==", "type": "package", "path": "mahapps.metro.iconpacks.mynauiicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.MynaUIIcons.dll", "lib/net462/MahApps.Metro.IconPacks.MynaUIIcons.xml", "lib/net47/MahApps.Metro.IconPacks.MynaUIIcons.dll", "lib/net47/MahApps.Metro.IconPacks.MynaUIIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MynaUIIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.MynaUIIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MynaUIIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.MynaUIIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.mynauiicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.mynauiicons.nuspec"]}, "MahApps.Metro.IconPacks.Octicons/6.1.0": {"sha512": "PkKOl3bn7q+TXcPWIvFMB2g9401+wEEvoOcp0g+0mf20ixeQBWkuGkw79Ff5VNv+DR+ZZ/9sG+a+2A1Yqd2mWA==", "type": "package", "path": "mahapps.metro.iconpacks.octicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Octicons.dll", "lib/net462/MahApps.Metro.IconPacks.Octicons.xml", "lib/net47/MahApps.Metro.IconPacks.Octicons.dll", "lib/net47/MahApps.Metro.IconPacks.Octicons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Octicons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Octicons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Octicons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Octicons.xml", "logo_small.png", "mahapps.metro.iconpacks.octicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.octicons.nuspec"]}, "MahApps.Metro.IconPacks.PhosphorIcons/6.1.0": {"sha512": "6wWeUZ+8w+quEzRF/mekPSQQsgvaLGSlT48lJecGkFTwJIY5wL7GL5ZwuLUVoN5xl1X5khF/IRAjm8Uf56oX/w==", "type": "package", "path": "mahapps.metro.iconpacks.phosphoricons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.PhosphorIcons.dll", "lib/net462/MahApps.Metro.IconPacks.PhosphorIcons.xml", "lib/net47/MahApps.Metro.IconPacks.PhosphorIcons.dll", "lib/net47/MahApps.Metro.IconPacks.PhosphorIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.PhosphorIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.PhosphorIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PhosphorIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PhosphorIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.phosphoricons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.phosphoricons.nuspec"]}, "MahApps.Metro.IconPacks.PicolIcons/6.1.0": {"sha512": "kF4teznJ9kCXHvFOddW4xl8WnVygAGleXrdPKcHTmWO+b1ChpBajQ0Z4zwIUs85E4zGmbY1z+f9eEjSKcK+DEQ==", "type": "package", "path": "mahapps.metro.iconpacks.picolicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.PicolIcons.dll", "lib/net462/MahApps.Metro.IconPacks.PicolIcons.xml", "lib/net47/MahApps.Metro.IconPacks.PicolIcons.dll", "lib/net47/MahApps.Metro.IconPacks.PicolIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.PicolIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.PicolIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PicolIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PicolIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.picolicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.picolicons.nuspec"]}, "MahApps.Metro.IconPacks.PixelartIcons/6.1.0": {"sha512": "heE6VJHrHO2zmhiHwNW5DZMA49RiFqhZZgRkmr6z8DLE/fyB2hQfHqYvq1h1FAif2SH8jIOj38FlC29iSfxMWA==", "type": "package", "path": "mahapps.metro.iconpacks.pixelarticons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.PixelartIcons.dll", "lib/net462/MahApps.Metro.IconPacks.PixelartIcons.xml", "lib/net47/MahApps.Metro.IconPacks.PixelartIcons.dll", "lib/net47/MahApps.Metro.IconPacks.PixelartIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.PixelartIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.PixelartIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PixelartIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.PixelartIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.pixelarticons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.pixelarticons.nuspec"]}, "MahApps.Metro.IconPacks.RadixIcons/6.1.0": {"sha512": "JYRskwSEKkt71YeiU/yOonqjUK75VGsQcNkewMuyyvZTWuOts0zl2ktwnWRHv2LCrkff5+KYvzYJTntOLvCMNw==", "type": "package", "path": "mahapps.metro.iconpacks.radixicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.RadixIcons.dll", "lib/net462/MahApps.Metro.IconPacks.RadixIcons.xml", "lib/net47/MahApps.Metro.IconPacks.RadixIcons.dll", "lib/net47/MahApps.Metro.IconPacks.RadixIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.RadixIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.RadixIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RadixIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RadixIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.radixicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.radixicons.nuspec"]}, "MahApps.Metro.IconPacks.RemixIcon/6.1.0": {"sha512": "JI6grQid7uradV2b/p+AN/aNprtBU2yHtGQZIWaRYoxwvVVdfWuoHMkKoJXTUmslKvSlKcnBcGguOW0eJgxSDw==", "type": "package", "path": "mahapps.metro.iconpacks.remixicon/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.RemixIcon.dll", "lib/net462/MahApps.Metro.IconPacks.RemixIcon.xml", "lib/net47/MahApps.Metro.IconPacks.RemixIcon.dll", "lib/net47/MahApps.Metro.IconPacks.RemixIcon.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.RemixIcon.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.RemixIcon.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RemixIcon.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RemixIcon.xml", "logo_small.png", "mahapps.metro.iconpacks.remixicon.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.remixicon.nuspec"]}, "MahApps.Metro.IconPacks.RPGAwesome/6.1.0": {"sha512": "XHarLjbZrr2EYkWXSOoKjCdiJnYdH/Fxx+m9401p9YAW1ECUoT6gokq4QXTSHdKiwjJ4/0ij70H6tSg0zaVIPA==", "type": "package", "path": "mahapps.metro.iconpacks.rpgawesome/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.RPGAwesome.dll", "lib/net462/MahApps.Metro.IconPacks.RPGAwesome.xml", "lib/net47/MahApps.Metro.IconPacks.RPGAwesome.dll", "lib/net47/MahApps.Metro.IconPacks.RPGAwesome.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.RPGAwesome.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.RPGAwesome.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RPGAwesome.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.RPGAwesome.xml", "logo_small.png", "mahapps.metro.iconpacks.rpgawesome.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.rpgawesome.nuspec"]}, "MahApps.Metro.IconPacks.SimpleIcons/6.1.0": {"sha512": "54XM8suu6YqQxHF418SZtlianeowziGtjT8BQp05gqZk+Rpm5opBeQ4o2fZ+5F39SqFHr62JQlx26H4Cl7Q6Jw==", "type": "package", "path": "mahapps.metro.iconpacks.simpleicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.SimpleIcons.dll", "lib/net462/MahApps.Metro.IconPacks.SimpleIcons.xml", "lib/net47/MahApps.Metro.IconPacks.SimpleIcons.dll", "lib/net47/MahApps.Metro.IconPacks.SimpleIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.SimpleIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.SimpleIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.SimpleIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.SimpleIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.simpleicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.simpleicons.nuspec"]}, "MahApps.Metro.IconPacks.Typicons/6.1.0": {"sha512": "MvA29YvOcSlNJeplNsHgbC9N+K96T1YIKyu9DddaRyfAmKC3D/PTUwMF8X6nz/fpl/+FJEo0/SukbLTGoegWtw==", "type": "package", "path": "mahapps.metro.iconpacks.typicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Typicons.dll", "lib/net462/MahApps.Metro.IconPacks.Typicons.xml", "lib/net47/MahApps.Metro.IconPacks.Typicons.dll", "lib/net47/MahApps.Metro.IconPacks.Typicons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Typicons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Typicons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Typicons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Typicons.xml", "logo_small.png", "mahapps.metro.iconpacks.typicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.typicons.nuspec"]}, "MahApps.Metro.IconPacks.Unicons/6.1.0": {"sha512": "qzKn0ACXgjvpDw0cJDJzFQ7OMGUDJqPIQ2fDBjSrjCtER4C83e1t53uZUAloSYa16YWxd4V/ptOIs/S1TD1AVg==", "type": "package", "path": "mahapps.metro.iconpacks.unicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Unicons.dll", "lib/net462/MahApps.Metro.IconPacks.Unicons.xml", "lib/net47/MahApps.Metro.IconPacks.Unicons.dll", "lib/net47/MahApps.Metro.IconPacks.Unicons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Unicons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Unicons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Unicons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Unicons.xml", "logo_small.png", "mahapps.metro.iconpacks.unicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.unicons.nuspec"]}, "MahApps.Metro.IconPacks.VaadinIcons/6.1.0": {"sha512": "srscbYzkVM+Lxk4/tiDsmGdSrrhoshbeatlwsclZbiDymLC4bGZ5vHQo48XVDENiLZhoSBnAeMG9MpHTVfxAfA==", "type": "package", "path": "mahapps.metro.iconpacks.vaadinicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.VaadinIcons.dll", "lib/net462/MahApps.Metro.IconPacks.VaadinIcons.xml", "lib/net47/MahApps.Metro.IconPacks.VaadinIcons.dll", "lib/net47/MahApps.Metro.IconPacks.VaadinIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.VaadinIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.VaadinIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.VaadinIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.VaadinIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.vaadinicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.vaadinicons.nuspec"]}, "MahApps.Metro.IconPacks.WeatherIcons/6.1.0": {"sha512": "1LDVlmG/7zEXUURVmZ0VRWsikU7jTVYV1RsrEb31H2oonJYjJvq62zl3ZU9SAvSl/LO8ztOqiZyLaRqO0DGw3g==", "type": "package", "path": "mahapps.metro.iconpacks.weathericons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.WeatherIcons.dll", "lib/net462/MahApps.Metro.IconPacks.WeatherIcons.xml", "lib/net47/MahApps.Metro.IconPacks.WeatherIcons.dll", "lib/net47/MahApps.Metro.IconPacks.WeatherIcons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.WeatherIcons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.WeatherIcons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.WeatherIcons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.WeatherIcons.xml", "logo_small.png", "mahapps.metro.iconpacks.weathericons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.weathericons.nuspec"]}, "MahApps.Metro.IconPacks.Zondicons/6.1.0": {"sha512": "ZaacyPWxwANY1wG4dryQVbLls71ihG0EQ6bSC14rfl/XqJ+Quh+Y7uzZInb+v3EB3ngrkk8j5xnEXCFfdShTxg==", "type": "package", "path": "mahapps.metro.iconpacks.zondicons/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/MahApps.Metro.IconPacks.Zondicons.dll", "lib/net462/MahApps.Metro.IconPacks.Zondicons.xml", "lib/net47/MahApps.Metro.IconPacks.Zondicons.dll", "lib/net47/MahApps.Metro.IconPacks.Zondicons.xml", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Zondicons.dll", "lib/net6.0-windows7.0/MahApps.Metro.IconPacks.Zondicons.xml", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Zondicons.dll", "lib/net8.0-windows7.0/MahApps.Metro.IconPacks.Zondicons.xml", "logo_small.png", "mahapps.metro.iconpacks.zondicons.6.1.0.nupkg.sha512", "mahapps.metro.iconpacks.zondicons.nuspec"]}, "MaterialDesignColors/5.2.1": {"sha512": "D0HW6E2/kzsnEWCh1KDG/K09Fpkvs9mR3n91Y8YSOsEAoQmGZbVAj58ssyAxGTiIPj2zB4ZVnwxkizwO35/v8A==", "type": "package", "path": "materialdesigncolors/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net8.0/MaterialDesignColors.dll", "lib/net8.0/MaterialDesignColors.pdb", "materialdesigncolors.5.2.1.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/5.2.1": {"sha512": "x8JDqNHJcTLLxIoVts3w7AbSq5Zo0FXTw89XqPN7+n0EKqLXFwWsywiUn08HDyTGAmZVJqbQsWKxKWCI8qfWsQ==", "type": "package", "path": "materialdesignthemes/5.2.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "docs/README.md", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net8.0/MaterialDesignThemes.Wpf.dll", "lib/net8.0/MaterialDesignThemes.Wpf.pdb", "lib/net8.0/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.5.2.1.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "MaterialDesignThemes.MahApps/5.2.1": {"sha512": "8lbnJKQjn3Ri6irNSl797AK3AqgtlYs7NjDsCXPYRhrua2ULb5qX2AY7F7vwpHDPRj8II+auW6trTxS0Im9iEw==", "type": "package", "path": "materialdesignthemes.mahapps/5.2.1", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/MaterialDesignThemes.MahApps.Icon.png", "lib/net462/MaterialDesignThemes.MahApps.dll", "lib/net462/MaterialDesignThemes.MahApps.pdb", "lib/net6.0/MaterialDesignThemes.MahApps.dll", "lib/net6.0/MaterialDesignThemes.MahApps.pdb", "lib/net8.0/MaterialDesignThemes.MahApps.dll", "lib/net8.0/MaterialDesignThemes.MahApps.pdb", "materialdesignthemes.mahapps.5.2.1.nupkg.sha512", "materialdesignthemes.mahapps.nuspec"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Platforms/2.0.0": {"sha512": "VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ==", "type": "package", "path": "microsoft.netcore.platforms/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.2.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"sha512": "8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/net5.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.1/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "NodeNetwork/6.0.0": {"sha512": "N3eOdUSSyxdylEmcQFXLBlb5J2nDROuWqW1gUYgdIrFNQ4KBmcrU0bo0P46TFKs2Cu2SYwLPJ1f/6jHlgitsDw==", "type": "package", "path": "nodenetwork/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NodeNetwork.dll", "lib/net472/NodeNetwork.xml", "lib/netcoreapp3.1/NodeNetwork.dll", "lib/netcoreapp3.1/NodeNetwork.xml", "nodenetwork.6.0.0.nupkg.sha512", "nodenetwork.nuspec"]}, "Pharmacist.Common/2.0.8": {"sha512": "NNiYvv2oTd18ckJMBt8zatCBPj0KwbzbxMuxgu45m20VtqvkvR6AborbOhkhmJC70iaSv3uLvhTnFExIJGDnQA==", "type": "package", "path": "pharmacist.common/2.0.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/net472/Pharmacist.Common.dll", "lib/net472/Pharmacist.Common.xml", "lib/net5.0/Pharmacist.Common.dll", "lib/net5.0/Pharmacist.Common.xml", "lib/netstandard2.0/Pharmacist.Common.dll", "lib/netstandard2.0/Pharmacist.Common.xml", "pharmacist.common.2.0.8.nupkg.sha512", "pharmacist.common.nuspec"]}, "ReactiveUI/13.2.18": {"sha512": "w457p8goRjJgbggK2whtgYO8U9SOT1JbMDyZOKceibWoZVuOGME4RfUc60/AsLVmfwlkI+9ve+xc2S696nJZuQ==", "type": "package", "path": "reactiveui/13.2.18", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/monoandroid10.0/ReactiveUI.dll", "lib/monoandroid10.0/ReactiveUI.pdb", "lib/monoandroid10.0/ReactiveUI.xml", "lib/net461/ReactiveUI.dll", "lib/net461/ReactiveUI.xml", "lib/net472/ReactiveUI.dll", "lib/net472/ReactiveUI.xml", "lib/net5.0-windows10.0.19041/ReactiveUI.dll", "lib/net5.0-windows10.0.19041/ReactiveUI.xml", "lib/net5.0-windows7.0/ReactiveUI.dll", "lib/net5.0-windows7.0/ReactiveUI.xml", "lib/net5.0/ReactiveUI.dll", "lib/net5.0/ReactiveUI.xml", "lib/netcoreapp3.1/ReactiveUI.dll", "lib/netcoreapp3.1/ReactiveUI.xml", "lib/netstandard2.0/ReactiveUI.dll", "lib/netstandard2.0/ReactiveUI.xml", "lib/tizen40/ReactiveUI.dll", "lib/tizen40/ReactiveUI.xml", "lib/uap10.0.16299/ReactiveUI.dll", "lib/uap10.0.16299/ReactiveUI.pri", "lib/uap10.0.16299/ReactiveUI.xml", "lib/xamarinios10/ReactiveUI.dll", "lib/xamarinios10/ReactiveUI.xml", "lib/xamarinmac20/ReactiveUI.dll", "lib/xamarinmac20/ReactiveUI.xml", "lib/xamarintvos10/ReactiveUI.dll", "lib/xamarintvos10/ReactiveUI.xml", "reactiveui.13.2.18.nupkg.sha512", "reactiveui.nuspec"]}, "ReactiveUI.Events.WPF/13.2.18": {"sha512": "m9mFgMdSWFAdlCEFOahD2bfDo+zV8BlLxOhTSVSEFbtiSC0loYHSbJDBZYu/nxTTYzjqHTN86PZ7T9NoRgfVGw==", "type": "package", "path": "reactiveui.events.wpf/13.2.18", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/net461/ReactiveUI.Events.WPF.dll", "lib/net461/ReactiveUI.Events.WPF.xml", "lib/net462/ReactiveUI.Events.WPF.dll", "lib/net462/ReactiveUI.Events.WPF.xml", "lib/net47/ReactiveUI.Events.WPF.dll", "lib/net47/ReactiveUI.Events.WPF.xml", "lib/net471/ReactiveUI.Events.WPF.dll", "lib/net471/ReactiveUI.Events.WPF.xml", "lib/net472/ReactiveUI.Events.WPF.dll", "lib/net472/ReactiveUI.Events.WPF.xml", "lib/net48/ReactiveUI.Events.WPF.dll", "lib/net48/ReactiveUI.Events.WPF.xml", "lib/net5.0-windows10.0.19041/ReactiveUI.Events.WPF.dll", "lib/net5.0-windows10.0.19041/ReactiveUI.Events.WPF.xml", "lib/net5.0-windows7.0/ReactiveUI.Events.WPF.dll", "lib/net5.0-windows7.0/ReactiveUI.Events.WPF.xml", "lib/netcoreapp3.1/ReactiveUI.Events.WPF.dll", "lib/netcoreapp3.1/ReactiveUI.Events.WPF.xml", "reactiveui.events.wpf.13.2.18.nupkg.sha512", "reactiveui.events.wpf.nuspec"]}, "ReactiveUI.WPF/13.2.18": {"sha512": "ALpQe9Y970v4d/RfFT/keb93POq1WEYwUq0bfvZchUiYG4j8GOcFe085x+D5bfwh2aCRz/hHNl+9fTFVnCRisw==", "type": "package", "path": "reactiveui.wpf/13.2.18", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/net461/ReactiveUI.Wpf.dll", "lib/net461/ReactiveUI.Wpf.xml", "lib/net472/ReactiveUI.Wpf.dll", "lib/net472/ReactiveUI.Wpf.xml", "lib/net48/ReactiveUI.Wpf.dll", "lib/net48/ReactiveUI.Wpf.xml", "lib/net5.0-windows10.0.19041/ReactiveUI.Wpf.dll", "lib/net5.0-windows10.0.19041/ReactiveUI.Wpf.xml", "lib/net5.0-windows7.0/ReactiveUI.Wpf.dll", "lib/net5.0-windows7.0/ReactiveUI.Wpf.xml", "lib/netcoreapp3.1/ReactiveUI.Wpf.dll", "lib/netcoreapp3.1/ReactiveUI.Wpf.xml", "reactiveui.wpf.13.2.18.nupkg.sha512", "reactiveui.wpf.nuspec"]}, "Splat/11.0.1": {"sha512": "C1p+fZE+xgwKCqnf9Qsk0lrJYGiUplWpS2wp9/RR/YNHkjx7vBCRebau1Uej7ZXda71eJgZVBYzZq0/qsQOc3Q==", "type": "package", "path": "splat/11.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/net461/Splat.dll", "lib/net461/Splat.xml", "lib/net5.0/Splat.dll", "lib/net5.0/Splat.xml", "lib/netstandard2.0/Splat.dll", "lib/netstandard2.0/Splat.xml", "splat.11.0.1.nupkg.sha512", "splat.nuspec"]}, "Splat.Drawing/11.0.1": {"sha512": "hSho9yLfiv2+QhWqZImg01lhZZzz/6qPP3r2AQQRRJdxIoqgcgw1VqBWYC5bT+4QzcX/hAcjtpmQXmYEg2KBZw==", "type": "package", "path": "splat.drawing/11.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/monoandroid90/Splat.Drawing.dll", "lib/monoandroid90/Splat.Drawing.pdb", "lib/monoandroid90/Splat.Drawing.xml", "lib/net461/Splat.Drawing.dll", "lib/net461/Splat.Drawing.xml", "lib/net5.0-windows7.0/Splat.Drawing.dll", "lib/net5.0-windows7.0/Splat.Drawing.xml", "lib/netcoreapp3.1/Splat.Drawing.dll", "lib/netcoreapp3.1/Splat.Drawing.xml", "lib/netstandard2.0/Splat.Drawing.dll", "lib/netstandard2.0/Splat.Drawing.xml", "lib/tizen40/Splat.Drawing.dll", "lib/tizen40/Splat.Drawing.xml", "lib/uap10.0.16299/Splat.Drawing.dll", "lib/uap10.0.16299/Splat.Drawing.pri", "lib/uap10.0.16299/Splat.Drawing.xml", "lib/xamarinios10/Splat.Drawing.dll", "lib/xamarinios10/Splat.Drawing.xml", "lib/xamarinmac20/Splat.Drawing.dll", "lib/xamarinmac20/Splat.Drawing.xml", "lib/xamarintvos10/Splat.Drawing.dll", "lib/xamarintvos10/Splat.Drawing.xml", "lib/xamarinwatchos10/Splat.Drawing.dll", "lib/xamarinwatchos10/Splat.Drawing.xml", "splat.drawing.11.0.1.nupkg.sha512", "splat.drawing.nuspec"]}, "StringMath/4.1.3": {"sha512": "XdhwElcvR9ovxyXzgG+m4sWTy1FiUYXbZg22g33EUS4/iP2xzcSvoLime2mL4salY2pE+KOz0haATFnPvs7bGQ==", "type": "package", "path": "stringmath/4.1.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net461/StringMath.dll", "lib/net461/StringMath.xml", "lib/net472/StringMath.dll", "lib/net472/StringMath.xml", "lib/net48/StringMath.dll", "lib/net48/StringMath.xml", "lib/net5.0/StringMath.dll", "lib/net5.0/StringMath.xml", "lib/net6.0/StringMath.dll", "lib/net6.0/StringMath.xml", "lib/net8.0/StringMath.dll", "lib/net8.0/StringMath.xml", "lib/netcoreapp3.1/StringMath.dll", "lib/netcoreapp3.1/StringMath.xml", "lib/netstandard2.1/StringMath.dll", "lib/netstandard2.1/StringMath.xml", "stringmath.4.1.3.nupkg.sha512", "stringmath.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/4.5.0": {"sha512": "UIFvaFfuKhLr9u5tWMxmVoDPkFeD+Qv8gUuap4aZgVGYSYMdERck4OhLN/2gulAc0nYTEigWXSJNNWshrmxnng==", "type": "package", "path": "system.configuration.configurationmanager/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "ref/net461/System.Configuration.ConfigurationManager.dll", "ref/net461/System.Configuration.ConfigurationManager.xml", "ref/netstandard2.0/System.Configuration.ConfigurationManager.dll", "ref/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.4.5.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Data.DataSetExtensions/4.5.0": {"sha512": "221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "type": "package", "path": "system.data.datasetextensions/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netstandard2.0/System.Data.DataSetExtensions.dll", "ref/net45/_._", "ref/netstandard2.0/System.Data.DataSetExtensions.dll", "system.data.datasetextensions.4.5.0.nupkg.sha512", "system.data.datasetextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.Contracts/4.3.0": {"sha512": "eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "type": "package", "path": "system.diagnostics.contracts/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Diagnostics.Contracts.dll", "lib/netstandard1.0/System.Diagnostics.Contracts.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Contracts.dll", "ref/netcore50/System.Diagnostics.Contracts.xml", "ref/netcore50/de/System.Diagnostics.Contracts.xml", "ref/netcore50/es/System.Diagnostics.Contracts.xml", "ref/netcore50/fr/System.Diagnostics.Contracts.xml", "ref/netcore50/it/System.Diagnostics.Contracts.xml", "ref/netcore50/ja/System.Diagnostics.Contracts.xml", "ref/netcore50/ko/System.Diagnostics.Contracts.xml", "ref/netcore50/ru/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hans/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hant/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/System.Diagnostics.Contracts.dll", "ref/netstandard1.0/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/de/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/es/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/fr/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/it/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ja/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ko/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ru/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Contracts.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Diagnostics.Contracts.dll", "system.diagnostics.contracts.4.3.0.nupkg.sha512", "system.diagnostics.contracts.nuspec"]}, "System.Drawing.Primitives/4.3.0": {"sha512": "1QU/c35gwdhvj77fkScXQQbjiVAqIL3fEYn/19NE0CV/ic5TN5PyWAft8HsrbRd4SBLEoErNCkWSzMDc0MmbRw==", "type": "package", "path": "system.drawing.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Drawing.Primitives.dll", "lib/netstandard1.1/System.Drawing.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Drawing.Primitives.dll", "ref/netstandard1.1/System.Drawing.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.drawing.primitives.4.3.0.nupkg.sha512", "system.drawing.primitives.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reactive/5.0.0": {"sha512": "erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "type": "package", "path": "system.reactive/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net5.0/_._", "build/netcoreapp3.1/System.Reactive.dll", "build/netcoreapp3.1/System.Reactive.targets", "build/netcoreapp3.1/System.Reactive.xml", "buildTransitive/net5.0/_._", "buildTransitive/netcoreapp3.1/System.Reactive.targets", "lib/net472/System.Reactive.dll", "lib/net472/System.Reactive.xml", "lib/net5.0-windows10.0.19041/System.Reactive.dll", "lib/net5.0-windows10.0.19041/System.Reactive.xml", "lib/net5.0/System.Reactive.dll", "lib/net5.0/System.Reactive.xml", "lib/netcoreapp3.1/_._", "lib/netstandard2.0/System.Reactive.dll", "lib/netstandard2.0/System.Reactive.xml", "lib/uap10.0.16299/System.Reactive.dll", "lib/uap10.0.16299/System.Reactive.pri", "lib/uap10.0.16299/System.Reactive.xml", "system.reactive.5.0.0.nupkg.sha512", "system.reactive.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"sha512": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "type": "package", "path": "system.runtime.compilerservices.unsafe/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Runtime.CompilerServices.Unsafe.dll", "lib/net45/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Serialization.Primitives/4.3.0": {"sha512": "Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "type": "package", "path": "system.runtime.serialization.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Primitives.dll", "lib/netcore50/System.Runtime.Serialization.Primitives.dll", "lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/de/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/es/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/fr/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/it/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ja/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ko/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ru/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.Serialization.Primitives.dll", "system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "system.runtime.serialization.primitives.nuspec"]}, "System.Security.AccessControl/4.5.0": {"sha512": "vW8Eoq0TMyz5vAG/6ce483x/CP83fgm4SJe5P8Tb1tZaobcvPrbMEL7rhH1DRdrYbbb6F0vq3OlzmK0Pkwks5A==", "type": "package", "path": "system.security.accesscontrol/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.5.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.ProtectedData/4.5.0": {"sha512": "wLBKzFnDCxP12VL9ANydSYhk59fC4cvOr9ypYQLPnAj48NQIhqnjdD2yhP8yEKyBJEjERWS9DisKL7rX5eU25Q==", "type": "package", "path": "system.security.cryptography.protecteddata/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.ProtectedData.dll", "lib/net461/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.dll", "ref/net461/System.Security.Cryptography.ProtectedData.xml", "ref/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "ref/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net461/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "system.security.cryptography.protecteddata.4.5.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Permissions/4.5.0": {"sha512": "9gdyuARhUR7H+p5CjyUB/zPk7/Xut3wUSP8NJQB6iZr8L3XUXTMdoLeVAg9N4rqF8oIpE7MpdqHdDHQ7XgJe0g==", "type": "package", "path": "system.security.permissions/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.dll", "ref/net461/System.Security.Permissions.dll", "ref/net461/System.Security.Permissions.xml", "ref/netstandard2.0/System.Security.Permissions.dll", "ref/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.4.5.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.5.0": {"sha512": "U77HfRXlZlOeIXd//Yoj6Jnk8AXlbeisf1oq1os+hxOGVnuG+lGSfGqTwTZBoORFF6j/0q7HXIl8cqwQ9aUGqQ==", "type": "package", "path": "system.security.principal.windows/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.5.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Extended.W<PERSON><PERSON> >= 5.0.0", "MahApps.Metro.IconPacks >= 6.1.0", "MaterialDesignColors >= 5.2.1", "MaterialDesignThemes >= 5.2.1", "MaterialDesignThemes.MahApps >= 5.2.1", "NodeNetwork >= 6.0.0", "StringMath >= 4.1.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\AITS3D_PRO.csproj", "projectName": "AITS3D_PRO", "projectPath": "E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\AITS3D_PRO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Extended.Wpf.Toolkit": {"target": "Package", "version": "[5.0.0, )"}, "MahApps.Metro.IconPacks": {"target": "Package", "version": "[6.1.0, )"}, "MaterialDesignColors": {"target": "Package", "version": "[5.2.1, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "MaterialDesignThemes.MahApps": {"target": "Package", "version": "[5.2.1, )"}, "NodeNetwork": {"target": "Package", "version": "[6.0.0, )"}, "StringMath": {"target": "Package", "version": "[4.1.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305/PortableRuntimeIdentifierGraph.json"}}}}