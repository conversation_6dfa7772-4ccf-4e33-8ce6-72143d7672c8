<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Default Theme Colors -->
    
    <!-- Background Colors -->
    <Color x:Key="PrimaryBackgroundColor">#FFFFFF</Color>
    <Color x:Key="SecondaryBackgroundColor">#F5F5F5</Color>
    <Color x:Key="AccentBackgroundColor">#E3F2FD</Color>
    
    <!-- Text Colors -->
    <Color x:Key="PrimaryTextColor">#212121</Color>
    <Color x:Key="SecondaryTextColor">#757575</Color>
    <Color x:Key="AccentTextColor">#1976D2</Color>
    
    <!-- Border Colors -->
    <Color x:Key="PrimaryBorderColor">#E0E0E0</Color>
    <Color x:Key="SecondaryBorderColor">#BDBDBD</Color>
    <Color x:Key="AccentBorderColor">#1976D2</Color>
    
    <!-- Button Colors -->
    <Color x:Key="ButtonBackgroundColor">#2196F3</Color>
    <Color x:Key="ButtonHoverColor">#1976D2</Color>
    <Color x:Key="ButtonPressedColor">#0D47A1</Color>
    <Color x:Key="ButtonTextColor">#FFFFFF</Color>
    
    <!-- Menu Colors -->
    <Color x:Key="MenuBackgroundColor">#FAFAFA</Color>
    <Color x:Key="MenuHoverColor">#E0E0E0</Color>
    <Color x:Key="MenuSelectedColor">#BBDEFB</Color>
    
    <!-- Status Colors -->
    <Color x:Key="SuccessColorColor">#4CAF50</Color>
    <Color x:Key="WarningColorColor">#FF9800</Color>
    <Color x:Key="ErrorColorColor">#F44336</Color>
    <Color x:Key="InfoColorColor">#2196F3</Color>

    <!-- Brush Resources -->
    
    <!-- Background Brushes -->
    <SolidColorBrush x:Key="PrimaryBackgroundBrush" Color="{DynamicResource PrimaryBackgroundColor}"/>
    <SolidColorBrush x:Key="SecondaryBackgroundBrush" Color="{DynamicResource SecondaryBackgroundColor}"/>
    <SolidColorBrush x:Key="AccentBackgroundBrush" Color="{DynamicResource AccentBackgroundColor}"/>
    
    <!-- Text Brushes -->
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{DynamicResource PrimaryTextColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{DynamicResource SecondaryTextColor}"/>
    <SolidColorBrush x:Key="AccentTextBrush" Color="{DynamicResource AccentTextColor}"/>
    
    <!-- Border Brushes -->
    <SolidColorBrush x:Key="PrimaryBorderBrush" Color="{DynamicResource PrimaryBorderColor}"/>
    <SolidColorBrush x:Key="SecondaryBorderBrush" Color="{DynamicResource SecondaryBorderColor}"/>
    <SolidColorBrush x:Key="AccentBorderBrush" Color="{DynamicResource AccentBorderColor}"/>
    
    <!-- Button Brushes -->
    <SolidColorBrush x:Key="ButtonBackgroundBrush" Color="{DynamicResource ButtonBackgroundColor}"/>
    <SolidColorBrush x:Key="ButtonHoverBrush" Color="{DynamicResource ButtonHoverColor}"/>
    <SolidColorBrush x:Key="ButtonPressedBrush" Color="{DynamicResource ButtonPressedColor}"/>
    <SolidColorBrush x:Key="ButtonTextBrush" Color="{DynamicResource ButtonTextColor}"/>
    
    <!-- Menu Brushes -->
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="{DynamicResource MenuBackgroundColor}"/>
    <SolidColorBrush x:Key="MenuHoverBrush" Color="{DynamicResource MenuHoverColor}"/>
    <SolidColorBrush x:Key="MenuSelectedBrush" Color="{DynamicResource MenuSelectedColor}"/>
    
    <!-- Status Brushes -->
    <SolidColorBrush x:Key="SuccessColorBrush" Color="{DynamicResource SuccessColorColor}"/>
    <SolidColorBrush x:Key="WarningColorBrush" Color="{DynamicResource WarningColorColor}"/>
    <SolidColorBrush x:Key="ErrorColorBrush" Color="{DynamicResource ErrorColorColor}"/>
    <SolidColorBrush x:Key="InfoColorBrush" Color="{DynamicResource InfoColorColor}"/>

</ResourceDictionary>
