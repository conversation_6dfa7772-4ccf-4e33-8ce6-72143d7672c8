using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Media;

namespace AITS3D_PRO.Models
{
    public class ColorTheme : INotifyPropertyChanged
    {
        private string _name;
        private string _description;
        private bool _isDarkTheme;
        private DateTime _createdDate;
        private DateTime _lastModified;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged();
            }
        }

        public bool IsDarkTheme
        {
            get => _isDarkTheme;
            set
            {
                _isDarkTheme = value;
                OnPropertyChanged();
            }
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set
            {
                _createdDate = value;
                OnPropertyChanged();
            }
        }

        public DateTime LastModified
        {
            get => _lastModified;
            set
            {
                _lastModified = value;
                OnPropertyChanged();
            }
        }

        public Dictionary<string, ColorProperty> Colors { get; set; }

        public ColorTheme()
        {
            Colors = new Dictionary<string, ColorProperty>();
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
            InitializeDefaultColors();
        }

        private void InitializeDefaultColors()
        {
            // Primary Colors
            Colors["PrimaryBackground"] = new ColorProperty("Primary Background", "#FFFFFF", "Main background color");
            Colors["SecondaryBackground"] = new ColorProperty("Secondary Background", "#F5F5F5", "Secondary background color");
            Colors["AccentBackground"] = new ColorProperty("Accent Background", "#E3F2FD", "Accent background color");

            // Text Colors
            Colors["PrimaryText"] = new ColorProperty("Primary Text", "#212121", "Main text color");
            Colors["SecondaryText"] = new ColorProperty("Secondary Text", "#757575", "Secondary text color");
            Colors["AccentText"] = new ColorProperty("Accent Text", "#1976D2", "Accent text color");

            // Border Colors
            Colors["PrimaryBorder"] = new ColorProperty("Primary Border", "#E0E0E0", "Main border color");
            Colors["SecondaryBorder"] = new ColorProperty("Secondary Border", "#BDBDBD", "Secondary border color");
            Colors["AccentBorder"] = new ColorProperty("Accent Border", "#1976D2", "Accent border color");

            // Button Colors
            Colors["ButtonBackground"] = new ColorProperty("Button Background", "#2196F3", "Button background color");
            Colors["ButtonHover"] = new ColorProperty("Button Hover", "#1976D2", "Button hover color");
            Colors["ButtonPressed"] = new ColorProperty("Button Pressed", "#0D47A1", "Button pressed color");
            Colors["ButtonText"] = new ColorProperty("Button Text", "#FFFFFF", "Button text color");

            // Menu Colors
            Colors["MenuBackground"] = new ColorProperty("Menu Background", "#FAFAFA", "Menu background color");
            Colors["MenuHover"] = new ColorProperty("Menu Hover", "#E0E0E0", "Menu hover color");
            Colors["MenuSelected"] = new ColorProperty("Menu Selected", "#BBDEFB", "Menu selected color");

            // Status Colors
            Colors["SuccessColor"] = new ColorProperty("Success Color", "#4CAF50", "Success status color");
            Colors["WarningColor"] = new ColorProperty("Warning Color", "#FF9800", "Warning status color");
            Colors["ErrorColor"] = new ColorProperty("Error Color", "#F44336", "Error status color");
            Colors["InfoColor"] = new ColorProperty("Info Color", "#2196F3", "Info status color");
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ColorProperty : INotifyPropertyChanged
    {
        private string _name;
        private string _hexValue;
        private string _description;
        private Color _color;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public string HexValue
        {
            get => _hexValue;
            set
            {
                _hexValue = value;
                UpdateColorFromHex();
                OnPropertyChanged();
                OnPropertyChanged(nameof(Color));
                OnPropertyChanged(nameof(Brush));
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                _description = value;
                OnPropertyChanged();
            }
        }

        public Color Color
        {
            get => _color;
            set
            {
                _color = value;
                _hexValue = value.ToString();
                OnPropertyChanged();
                OnPropertyChanged(nameof(HexValue));
                OnPropertyChanged(nameof(Brush));
            }
        }

        public SolidColorBrush Brush => new SolidColorBrush(Color);

        public ColorProperty()
        {
        }

        public ColorProperty(string name, string hexValue, string description = "")
        {
            Name = name;
            Description = description;
            HexValue = hexValue;
        }

        private void UpdateColorFromHex()
        {
            try
            {
                _color = (Color)ColorConverter.ConvertFromString(_hexValue);
            }
            catch
            {
                _color = Colors.Black;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
