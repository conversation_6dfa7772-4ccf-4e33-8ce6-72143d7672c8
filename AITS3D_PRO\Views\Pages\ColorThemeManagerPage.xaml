<UserControl x:Class="AITS3D_PRO.Views.Pages.ColorThemeManagerPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AITS3D_PRO.Views.Pages"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
        </Style>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource SecondaryBackgroundBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <Style x:Key="ColorPreviewStyle" TargetType="Rectangle">
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Stroke" Value="{DynamicResource PrimaryBorderBrush}"/>
            <Setter Property="StrokeThickness" Value="1"/>
            <Setter Property="RadiusX" Value="4"/>
            <Setter Property="RadiusY" Value="4"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="{DynamicResource PrimaryBackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <StackPanel>
                <TextBlock Text="Color Theme Manager" FontSize="24" FontWeight="Bold" 
                          Foreground="{DynamicResource PrimaryTextBrush}" HorizontalAlignment="Center"/>
                <TextBlock Text="Manage application themes, colors, and visual settings" 
                          FontSize="14" Foreground="{DynamicResource SecondaryTextBrush}" 
                          HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="10,5,10,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Theme List -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Available Themes" Style="{StaticResource SectionHeaderStyle}"/>

                    <!-- Theme Actions -->
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5">
                        <Button Content="New" Command="{Binding NewThemeCommand}" Margin="0,0,5,0" Padding="10,5"/>
                        <Button Content="Load Predefined" Command="{Binding LoadPredefinedThemesCommand}" Padding="10,5"/>
                    </StackPanel>

                    <!-- Theme List -->
                    <ListBox Grid.Row="2" ItemsSource="{Binding AvailableThemes}" 
                            SelectedItem="{Binding SelectedTheme}" Margin="0,5">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Margin="5">
                                    <TextBlock Text="{Binding Name}" FontWeight="Bold" 
                                              Foreground="{DynamicResource PrimaryTextBrush}"/>
                                    <TextBlock Text="{Binding Description}" FontSize="12" 
                                              Foreground="{DynamicResource SecondaryTextBrush}"/>
                                    <TextBlock Text="{Binding LastModified, StringFormat='Modified: {0:MM/dd/yyyy}'}" 
                                              FontSize="10" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <!-- Theme Management Buttons -->
                    <StackPanel Grid.Row="3" Margin="0,10,0,0">
                        <Button Content="Load Theme" Command="{Binding LoadThemeCommand}" Margin="0,2" Padding="10,5"/>
                        <Button Content="Delete Theme" Command="{Binding DeleteThemeCommand}" Margin="0,2" Padding="10,5"/>
                        <Separator Margin="0,5"/>
                        <Button Content="Import JSON" Command="{Binding ImportJsonCommand}" Margin="0,2" Padding="10,5"/>
                        <Button Content="Import CSV" Command="{Binding ImportCsvCommand}" Margin="0,2" Padding="10,5"/>
                        <Button Content="Export JSON" Command="{Binding ExportJsonCommand}" Margin="0,2" Padding="10,5"/>
                        <Button Content="Export CSV" Command="{Binding ExportCsvCommand}" Margin="0,2" Padding="10,5"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Center Panel - Color Editor -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Color Properties" Style="{StaticResource SectionHeaderStyle}"/>

                    <!-- Search Box -->
                    <TextBox Grid.Row="1" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" 
                            Margin="0,5" Padding="8" 
                            Tag="Search colors..." Background="{DynamicResource SecondaryBackgroundBrush}"/>

                    <!-- Color List -->
                    <ScrollViewer Grid.Row="2" Margin="0,5">
                        <ItemsControl ItemsSource="{Binding FilteredColors}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                                           BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                                           BorderThickness="1" CornerRadius="4" Margin="0,2" Padding="10">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Color Preview -->
                                            <Rectangle Grid.Column="0" Style="{StaticResource ColorPreviewStyle}" 
                                                      Fill="{Binding Brush}" Margin="0,0,10,0"/>

                                            <!-- Color Info -->
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" 
                                                          Foreground="{DynamicResource PrimaryTextBrush}"/>
                                                <TextBlock Text="{Binding Description}" FontSize="12" 
                                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                                            </StackPanel>

                                            <!-- Color Value -->
                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <TextBox Text="{Binding HexValue, UpdateSourceTrigger=PropertyChanged}" 
                                                        Width="80" Margin="5,0"/>
                                                <Button Content="🎨" Width="30" Height="25" 
                                                       Click="ColorPicker_Click" Tag="{Binding}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <Button Content="Apply Theme" Command="{Binding ApplyThemeCommand}" 
                               Margin="5,0" Padding="15,8" Background="#4CAF50" Foreground="White"/>
                        <Button Content="Reset to Default" Command="{Binding ResetToDefaultCommand}" 
                               Margin="5,0" Padding="15,8"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Right Panel - Theme Editor -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="Theme Editor" Style="{StaticResource SectionHeaderStyle}"/>

                    <!-- Theme Properties -->
                    <ScrollViewer Grid.Row="1" Margin="0,5">
                        <StackPanel>
                            <TextBlock Text="Theme Name" FontWeight="Bold" Margin="0,5,0,2" 
                                      Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <TextBox Text="{Binding NewThemeName, UpdateSourceTrigger=PropertyChanged}" 
                                    IsEnabled="{Binding IsEditMode}" Padding="8" Margin="0,0,0,10"/>

                            <TextBlock Text="Description" FontWeight="Bold" Margin="0,5,0,2" 
                                      Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <TextBox Text="{Binding NewThemeDescription, UpdateSourceTrigger=PropertyChanged}" 
                                    IsEnabled="{Binding IsEditMode}" Padding="8" Height="60" 
                                    TextWrapping="Wrap" AcceptsReturn="True" Margin="0,0,0,10"/>

                            <CheckBox Content="Dark Theme" IsChecked="{Binding CurrentTheme.IsDarkTheme}" 
                                     IsEnabled="{Binding IsEditMode}" Margin="0,5"/>

                            <Separator Margin="0,15"/>

                            <!-- Theme Info -->
                            <TextBlock Text="Theme Information" FontWeight="Bold" Margin="0,5,0,10" 
                                      Foreground="{DynamicResource PrimaryTextBrush}"/>
                            
                            <StackPanel Margin="0,5">
                                <TextBlock Text="Created:" FontWeight="Bold" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                                <TextBlock Text="{Binding CurrentTheme.CreatedDate, StringFormat=MM/dd/yyyy HH:mm}"
                                          Margin="10,0,0,5" Foreground="{DynamicResource SecondaryTextBrush}"/>

                                <TextBlock Text="Last Modified:" FontWeight="Bold"
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                                <TextBlock Text="{Binding CurrentTheme.LastModified, StringFormat=MM/dd/yyyy HH:mm}"
                                          Margin="10,0,0,5" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                
                                <TextBlock Text="Total Colors:" FontWeight="Bold" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                                <TextBlock Text="{Binding CurrentTheme.Colors.Count}" 
                                          Margin="10,0,0,5" Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>

                            <!-- Color Categories Preview -->
                            <TextBlock Text="Color Preview" FontWeight="Bold" Margin="0,15,0,5" 
                                      Foreground="{DynamicResource PrimaryTextBrush}"/>
                            
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Background Colors -->
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="2">
                                    <TextBlock Text="Backgrounds" FontSize="10" FontWeight="Bold"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#FFFFFF" Stroke="Gray"/>
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#F5F5F5" Stroke="Gray"/>
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#E3F2FD" Stroke="Gray"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Text Colors -->
                                <StackPanel Grid.Row="0" Grid.Column="1" Margin="2">
                                    <TextBlock Text="Text" FontSize="10" FontWeight="Bold"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#212121" Stroke="Gray"/>
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#757575" Stroke="Gray"/>
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#1976D2" Stroke="Gray"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Button Colors -->
                                <StackPanel Grid.Row="1" Grid.Column="0" Margin="2">
                                    <TextBlock Text="Buttons" FontSize="10" FontWeight="Bold"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#2196F3" Stroke="Gray"/>
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#1976D2" Stroke="Gray"/>
                                        <Rectangle Width="20" Height="20" Margin="1" Fill="#0D47A1" Stroke="Gray"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Status Colors -->
                                <StackPanel Grid.Row="1" Grid.Column="1" Margin="2">
                                    <TextBlock Text="Status" FontSize="10" FontWeight="Bold"/>
                                    <StackPanel Orientation="Horizontal">
                                        <Rectangle Width="15" Height="20" Margin="1" Fill="#4CAF50" Stroke="Gray"/>
                                        <Rectangle Width="15" Height="20" Margin="1" Fill="#FF9800" Stroke="Gray"/>
                                        <Rectangle Width="15" Height="20" Margin="1" Fill="#F44336" Stroke="Gray"/>
                                        <Rectangle Width="15" Height="20" Margin="1" Fill="#2196F3" Stroke="Gray"/>
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Edit Buttons -->
                    <StackPanel Grid.Row="2" Margin="0,10,0,0">
                        <Button Content="Edit Theme" Command="{Binding EditThemeCommand}" 
                               Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Inverse}"
                               Margin="0,2" Padding="10,5"/>
                        
                        <StackPanel Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Button Content="Save Theme" Command="{Binding SaveThemeCommand}" 
                                   Margin="0,2" Padding="10,5" Background="#4CAF50" Foreground="White"/>
                            <Button Content="Cancel" Command="{Binding CancelEditCommand}" 
                                   Margin="0,2" Padding="10,5"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
