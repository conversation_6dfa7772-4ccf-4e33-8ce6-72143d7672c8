using AITS3D_PRO.ViewModels.Windows;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;


namespace AITS3D_PRO
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool IsMaximized = false;
        private bool isSidebarVisible = true;
        private bool isTerminalVisible = true;
        public MainViewModel ViewModel { get; set; }

        public MainWindow()
        {
            InitializeComponent();
            ViewModel = new MainViewModel();
            this.DataContext = ViewModel;
        }
        

        private void Border_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
                this.DragMove();
            }
        }
        private void Border_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                if (IsMaximized)
                {
                    this.WindowState = WindowState.Normal;
                    this.Height = 800; // Set to your desired height
                    this.Width = 1080; // Set to your desired width
                    IsMaximized = false;
                }
                else
                {
                    this.WindowState = WindowState.Maximized;
                    IsMaximized = true;
                }
            }
        }

        // Xử lý sự kiện button menu click
        private void MenuButton_Click(object sender, RoutedEventArgs e)
        {
            // Tìm Grid chứa các columns
            var mainGrid = this.FindName("MainGrid") as Grid;
            if (mainGrid == null) return;

            if (isSidebarVisible)
            {
                // Ẩn full menu (col 1), hiện icon menu (col 0)
                mainGrid.ColumnDefinitions[1].Width = new GridLength(0);
                mainGrid.ColumnDefinitions[0].Width = new GridLength(80);
            }
            else
            {
                // Hiện full menu (col 1), ẩn icon menu (col 0)
                mainGrid.ColumnDefinitions[1].Width = new GridLength(200);
                mainGrid.ColumnDefinitions[0].Width = new GridLength(0);
            }

            isSidebarVisible = !isSidebarVisible;
        }

        private void TerminalButton_Click(object sender, RoutedEventArgs e)
        {
            // Tìm Grid chứa các columns
            var mainSection = this.FindName("MainSection") as Grid;
            if (mainSection == null) return;

            if (isTerminalVisible)
            {
                // Ẩn full menu (row 2)
                mainSection.RowDefinitions[2].Height = new GridLength(200);
            }
            else
            {
                // Hiện full menu (row 2)
                mainSection.RowDefinitions[2].Height = new GridLength(60);
            }

            isTerminalVisible = !isTerminalVisible;
        }

    }
}