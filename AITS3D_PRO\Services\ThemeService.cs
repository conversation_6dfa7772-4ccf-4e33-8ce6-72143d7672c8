using AITS3D_PRO.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Media;

namespace AITS3D_PRO.Services
{
    public class ThemeService
    {
        private static ThemeService _instance;
        public static ThemeService Instance => _instance ??= new ThemeService();

        private ColorTheme _currentTheme;
        private readonly string _themesDirectory;
        private readonly string _currentThemeFile;

        public event EventHandler<ColorTheme> ThemeChanged;

        public ColorTheme CurrentTheme
        {
            get => _currentTheme;
            private set
            {
                _currentTheme = value;
                ThemeChanged?.Invoke(this, value);
            }
        }

        private ThemeService()
        {
            _themesDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Themes");
            _currentThemeFile = Path.Combine(_themesDirectory, "current_theme.json");
            
            if (!Directory.Exists(_themesDirectory))
            {
                Directory.CreateDirectory(_themesDirectory);
            }

            LoadCurrentTheme();
        }

        public void ApplyTheme(ColorTheme theme)
        {
            CurrentTheme = theme;
            UpdateApplicationResources();
            SaveCurrentTheme();
        }

        private void UpdateApplicationResources()
        {
            if (CurrentTheme == null) return;

            var app = Application.Current;
            if (app?.Resources == null) return;

            foreach (var colorPair in CurrentTheme.Colors)
            {
                var key = colorPair.Key;
                var colorProperty = colorPair.Value;

                // Update Color resources
                app.Resources[key + "Color"] = colorProperty.Color;
                
                // Update Brush resources
                app.Resources[key + "Brush"] = new SolidColorBrush(colorProperty.Color);
            }
        }

        public List<ColorTheme> GetAvailableThemes()
        {
            var themes = new List<ColorTheme>();
            
            if (!Directory.Exists(_themesDirectory))
                return themes;

            var themeFiles = Directory.GetFiles(_themesDirectory, "*.json")
                .Where(f => !f.EndsWith("current_theme.json"));

            foreach (var file in themeFiles)
            {
                try
                {
                    var theme = LoadThemeFromFile(file);
                    if (theme != null)
                        themes.Add(theme);
                }
                catch (Exception ex)
                {
                    // Log error if needed
                    Console.WriteLine($"Error loading theme from {file}: {ex.Message}");
                }
            }

            return themes;
        }

        public void SaveTheme(ColorTheme theme)
        {
            if (theme == null) return;

            theme.LastModified = DateTime.Now;
            var fileName = $"{SanitizeFileName(theme.Name)}.json";
            var filePath = Path.Combine(_themesDirectory, fileName);

            var json = JsonConvert.SerializeObject(theme, Formatting.Indented);
            File.WriteAllText(filePath, json);
        }

        public void DeleteTheme(string themeName)
        {
            var fileName = $"{SanitizeFileName(themeName)}.json";
            var filePath = Path.Combine(_themesDirectory, fileName);

            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }

        public ColorTheme LoadThemeFromFile(string filePath)
        {
            if (!File.Exists(filePath)) return null;

            try
            {
                var json = File.ReadAllText(filePath);
                return JsonConvert.DeserializeObject<ColorTheme>(json);
            }
            catch
            {
                return null;
            }
        }

        public void ExportThemeToJson(ColorTheme theme, string filePath)
        {
            var json = JsonConvert.SerializeObject(theme, Formatting.Indented);
            File.WriteAllText(filePath, json);
        }

        public void ExportThemeToCsv(ColorTheme theme, string filePath)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Property,Name,HexValue,Description");

            foreach (var colorPair in theme.Colors)
            {
                var key = colorPair.Key;
                var color = colorPair.Value;
                csv.AppendLine($"{key},{color.Name},{color.HexValue},{color.Description}");
            }

            File.WriteAllText(filePath, csv.ToString());
        }

        public ColorTheme ImportThemeFromJson(string filePath)
        {
            return LoadThemeFromFile(filePath);
        }

        public ColorTheme ImportThemeFromCsv(string filePath)
        {
            if (!File.Exists(filePath)) return null;

            try
            {
                var lines = File.ReadAllLines(filePath);
                if (lines.Length < 2) return null;

                var theme = new ColorTheme
                {
                    Name = Path.GetFileNameWithoutExtension(filePath),
                    Description = "Imported from CSV",
                    Colors = new Dictionary<string, ColorProperty>()
                };

                for (int i = 1; i < lines.Length; i++)
                {
                    var parts = lines[i].Split(',');
                    if (parts.Length >= 3)
                    {
                        var key = parts[0].Trim();
                        var name = parts[1].Trim();
                        var hexValue = parts[2].Trim();
                        var description = parts.Length > 3 ? parts[3].Trim() : "";

                        theme.Colors[key] = new ColorProperty(name, hexValue, description);
                    }
                }

                return theme;
            }
            catch
            {
                return null;
            }
        }

        private void LoadCurrentTheme()
        {
            if (File.Exists(_currentThemeFile))
            {
                CurrentTheme = LoadThemeFromFile(_currentThemeFile);
            }

            if (CurrentTheme == null)
            {
                CurrentTheme = CreateDefaultTheme();
                SaveCurrentTheme();
            }

            UpdateApplicationResources();
        }

        private void SaveCurrentTheme()
        {
            if (CurrentTheme != null)
            {
                var json = JsonConvert.SerializeObject(CurrentTheme, Formatting.Indented);
                File.WriteAllText(_currentThemeFile, json);
            }
        }

        private ColorTheme CreateDefaultTheme()
        {
            return new ColorTheme
            {
                Name = "Default Theme",
                Description = "Default application theme",
                IsDarkTheme = false
            };
        }

        private string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        }

        public List<ColorTheme> GetPredefinedThemes()
        {
            var themes = new List<ColorTheme>();

            // Light Theme
            var lightTheme = new ColorTheme
            {
                Name = "Light Theme",
                Description = "Clean light theme",
                IsDarkTheme = false
            };
            themes.Add(lightTheme);

            // Dark Theme
            var darkTheme = new ColorTheme
            {
                Name = "Dark Theme",
                Description = "Modern dark theme",
                IsDarkTheme = true
            };
            
            // Override default colors for dark theme
            darkTheme.Colors["PrimaryBackground"] = new ColorProperty("Primary Background", "#1E1E1E", "Main background color");
            darkTheme.Colors["SecondaryBackground"] = new ColorProperty("Secondary Background", "#2D2D30", "Secondary background color");
            darkTheme.Colors["AccentBackground"] = new ColorProperty("Accent Background", "#3F3F46", "Accent background color");
            darkTheme.Colors["PrimaryText"] = new ColorProperty("Primary Text", "#FFFFFF", "Main text color");
            darkTheme.Colors["SecondaryText"] = new ColorProperty("Secondary Text", "#CCCCCC", "Secondary text color");
            darkTheme.Colors["AccentText"] = new ColorProperty("Accent Text", "#569CD6", "Accent text color");
            darkTheme.Colors["PrimaryBorder"] = new ColorProperty("Primary Border", "#3F3F46", "Main border color");
            darkTheme.Colors["SecondaryBorder"] = new ColorProperty("Secondary Border", "#555555", "Secondary border color");
            darkTheme.Colors["MenuBackground"] = new ColorProperty("Menu Background", "#252526", "Menu background color");
            darkTheme.Colors["MenuHover"] = new ColorProperty("Menu Hover", "#3F3F46", "Menu hover color");
            darkTheme.Colors["MenuSelected"] = new ColorProperty("Menu Selected", "#094771", "Menu selected color");

            themes.Add(darkTheme);

            // Blue Theme
            var blueTheme = new ColorTheme
            {
                Name = "Blue Theme",
                Description = "Professional blue theme",
                IsDarkTheme = false
            };
            
            blueTheme.Colors["AccentBackground"] = new ColorProperty("Accent Background", "#E3F2FD", "Accent background color");
            blueTheme.Colors["AccentText"] = new ColorProperty("Accent Text", "#0D47A1", "Accent text color");
            blueTheme.Colors["AccentBorder"] = new ColorProperty("Accent Border", "#1976D2", "Accent border color");
            blueTheme.Colors["ButtonBackground"] = new ColorProperty("Button Background", "#1976D2", "Button background color");
            blueTheme.Colors["MenuSelected"] = new ColorProperty("Menu Selected", "#BBDEFB", "Menu selected color");

            themes.Add(blueTheme);

            return themes;
        }
    }
}
