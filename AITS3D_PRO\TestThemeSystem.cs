using AITS3D_PRO.Models;
using AITS3D_PRO.Services;
using System;
using System.Windows.Media;

namespace AITS3D_PRO
{
    /// <summary>
    /// Class để test Theme System - có thể xóa sau khi test xong
    /// </summary>
    public static class TestThemeSystem
    {
        public static void RunTests()
        {
            Console.WriteLine("=== Testing Theme System ===");

            // Test 1: Create and save theme
            TestCreateAndSaveTheme();

            // Test 2: Load themes
            TestLoadThemes();

            // Test 3: Apply theme
            TestApplyTheme();

            // Test 4: Export/Import
            TestExportImport();

            Console.WriteLine("=== All tests completed ===");
        }

        private static void TestCreateAndSaveTheme()
        {
            Console.WriteLine("\n1. Testing Create and Save Theme...");

            var theme = new ColorTheme
            {
                Name = "Test Theme",
                Description = "Theme for testing",
                IsDarkTheme = false
            };

            // Modify some colors
            theme.Colors["PrimaryBackground"] = new ColorProperty("Primary Background", "#F0F0F0", "Light gray background");
            theme.Colors["AccentText"] = new ColorProperty("Accent Text", "#FF5722", "Orange accent");

            // Save theme
            ThemeService.Instance.SaveTheme(theme);
            Console.WriteLine($"✓ Created and saved theme: {theme.Name}");
        }

        private static void TestLoadThemes()
        {
            Console.WriteLine("\n2. Testing Load Themes...");

            var themes = ThemeService.Instance.GetAvailableThemes();
            Console.WriteLine($"✓ Found {themes.Count} themes:");

            foreach (var theme in themes)
            {
                Console.WriteLine($"  - {theme.Name}: {theme.Description}");
            }
        }

        private static void TestApplyTheme()
        {
            Console.WriteLine("\n3. Testing Apply Theme...");

            var themes = ThemeService.Instance.GetAvailableThemes();
            if (themes.Count > 0)
            {
                var firstTheme = themes[0];
                ThemeService.Instance.ApplyTheme(firstTheme);
                Console.WriteLine($"✓ Applied theme: {firstTheme.Name}");
            }
            else
            {
                Console.WriteLine("⚠ No themes available to apply");
            }
        }

        private static void TestExportImport()
        {
            Console.WriteLine("\n4. Testing Export/Import...");

            var currentTheme = ThemeService.Instance.CurrentTheme;
            if (currentTheme != null)
            {
                try
                {
                    // Test JSON export
                    var jsonPath = System.IO.Path.Combine(
                        System.IO.Path.GetTempPath(), 
                        "test_theme.json"
                    );
                    ThemeService.Instance.ExportThemeToJson(currentTheme, jsonPath);
                    Console.WriteLine($"✓ Exported theme to JSON: {jsonPath}");

                    // Test CSV export
                    var csvPath = System.IO.Path.Combine(
                        System.IO.Path.GetTempPath(), 
                        "test_theme.csv"
                    );
                    ThemeService.Instance.ExportThemeToCsv(currentTheme, csvPath);
                    Console.WriteLine($"✓ Exported theme to CSV: {csvPath}");

                    // Test JSON import
                    var importedTheme = ThemeService.Instance.ImportThemeFromJson(jsonPath);
                    if (importedTheme != null)
                    {
                        Console.WriteLine($"✓ Imported theme from JSON: {importedTheme.Name}");
                    }

                    // Test CSV import
                    var importedCsvTheme = ThemeService.Instance.ImportThemeFromCsv(csvPath);
                    if (importedCsvTheme != null)
                    {
                        Console.WriteLine($"✓ Imported theme from CSV: {importedCsvTheme.Name}");
                    }

                    // Cleanup
                    System.IO.File.Delete(jsonPath);
                    System.IO.File.Delete(csvPath);
                    Console.WriteLine("✓ Cleaned up test files");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Export/Import test failed: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("⚠ No current theme to test export/import");
            }
        }

        public static void CreateSampleThemes()
        {
            Console.WriteLine("\n=== Creating Sample Themes ===");

            // Create and save predefined themes
            var predefinedThemes = ThemeService.Instance.GetPredefinedThemes();
            foreach (var theme in predefinedThemes)
            {
                ThemeService.Instance.SaveTheme(theme);
                Console.WriteLine($"✓ Created sample theme: {theme.Name}");
            }

            // Create a custom theme
            var customTheme = new ColorTheme
            {
                Name = "Purple Theme",
                Description = "Custom purple theme",
                IsDarkTheme = false
            };

            // Customize colors
            customTheme.Colors["AccentBackground"] = new ColorProperty("Accent Background", "#E1BEE7", "Light purple");
            customTheme.Colors["AccentText"] = new ColorProperty("Accent Text", "#7B1FA2", "Dark purple");
            customTheme.Colors["AccentBorder"] = new ColorProperty("Accent Border", "#9C27B0", "Purple border");
            customTheme.Colors["ButtonBackground"] = new ColorProperty("Button Background", "#9C27B0", "Purple button");
            customTheme.Colors["MenuSelected"] = new ColorProperty("Menu Selected", "#CE93D8", "Light purple selection");

            ThemeService.Instance.SaveTheme(customTheme);
            Console.WriteLine($"✓ Created custom theme: {customTheme.Name}");

            Console.WriteLine("=== Sample themes created ===");
        }

        public static void PrintCurrentThemeInfo()
        {
            Console.WriteLine("\n=== Current Theme Info ===");

            var currentTheme = ThemeService.Instance.CurrentTheme;
            if (currentTheme != null)
            {
                Console.WriteLine($"Name: {currentTheme.Name}");
                Console.WriteLine($"Description: {currentTheme.Description}");
                Console.WriteLine($"Is Dark Theme: {currentTheme.IsDarkTheme}");
                Console.WriteLine($"Created: {currentTheme.CreatedDate}");
                Console.WriteLine($"Last Modified: {currentTheme.LastModified}");
                Console.WriteLine($"Total Colors: {currentTheme.Colors.Count}");

                Console.WriteLine("\nColors:");
                foreach (var colorPair in currentTheme.Colors)
                {
                    var color = colorPair.Value;
                    Console.WriteLine($"  {colorPair.Key}: {color.HexValue} ({color.Name})");
                }
            }
            else
            {
                Console.WriteLine("No current theme loaded");
            }

            Console.WriteLine("=== End Theme Info ===");
        }
    }
}
