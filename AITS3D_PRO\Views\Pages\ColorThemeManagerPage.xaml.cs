using AITS3D_PRO.Models;
using AITS3D_PRO.ViewModels.Pages;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AITS3D_PRO.Views.Pages
{
    /// <summary>
    /// Interaction logic for ColorThemeManagerPage.xaml
    /// </summary>
    public partial class ColorThemeManagerPage : UserControl
    {
        public ColorThemeManagerViewModel ViewModel { get; set; }

        public ColorThemeManagerPage()
        {
            InitializeComponent();
            ViewModel = new ColorThemeManagerViewModel();
            DataContext = ViewModel;
        }

        private void ColorPicker_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var colorProperty = button?.Tag as ColorProperty;
            
            if (colorProperty == null) return;

            // Create a simple color picker dialog
            var colorPickerWindow = new ColorPickerWindow(colorProperty.Color);
            colorPickerWindow.Owner = Window.GetWindow(this);
            
            if (colorPickerWindow.ShowDialog() == true)
            {
                colorProperty.Color = colorPickerWindow.SelectedColor;
            }
        }
    }

    /// <summary>
    /// Simple color picker window
    /// </summary>
    public partial class ColorPickerWindow : Window
    {
        public Color SelectedColor { get; private set; }

        private readonly ColorProperty _colorProperty;

        public ColorPickerWindow(Color initialColor)
        {
            InitializeComponent();
            SelectedColor = initialColor;
            SetupColorPicker();
        }

        private void InitializeComponent()
        {
            Title = "Color Picker";
            Width = 400;
            Height = 300;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            ResizeMode = ResizeMode.NoResize;

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Color preview and sliders
            var colorPanel = CreateColorPanel();
            Grid.SetRow(colorPanel, 0);
            mainGrid.Children.Add(colorPanel);

            // Buttons
            var buttonPanel = CreateButtonPanel();
            Grid.SetRow(buttonPanel, 1);
            mainGrid.Children.Add(buttonPanel);

            Content = mainGrid;
        }

        private Panel CreateColorPanel()
        {
            var panel = new StackPanel { Margin = new Thickness(20) };

            // Color preview
            var previewBorder = new Border
            {
                Width = 100,
                Height = 50,
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1),
                Margin = new Thickness(0, 0, 0, 20),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            
            var previewRect = new System.Windows.Shapes.Rectangle
            {
                Fill = new SolidColorBrush(SelectedColor)
            };
            previewBorder.Child = previewRect;
            panel.Children.Add(previewBorder);

            // RGB Sliders
            var rgbPanel = new StackPanel();
            
            // Red slider
            var redPanel = CreateSliderPanel("Red:", SelectedColor.R, (value) => {
                SelectedColor = Color.FromRgb((byte)value, SelectedColor.G, SelectedColor.B);
                previewRect.Fill = new SolidColorBrush(SelectedColor);
            });
            rgbPanel.Children.Add(redPanel);

            // Green slider
            var greenPanel = CreateSliderPanel("Green:", SelectedColor.G, (value) => {
                SelectedColor = Color.FromRgb(SelectedColor.R, (byte)value, SelectedColor.B);
                previewRect.Fill = new SolidColorBrush(SelectedColor);
            });
            rgbPanel.Children.Add(greenPanel);

            // Blue slider
            var bluePanel = CreateSliderPanel("Blue:", SelectedColor.B, (value) => {
                SelectedColor = Color.FromRgb(SelectedColor.R, SelectedColor.G, (byte)value);
                previewRect.Fill = new SolidColorBrush(SelectedColor);
            });
            rgbPanel.Children.Add(bluePanel);

            panel.Children.Add(rgbPanel);

            // Hex input
            var hexPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 10, 0, 0) };
            hexPanel.Children.Add(new TextBlock { Text = "Hex:", VerticalAlignment = VerticalAlignment.Center, Margin = new Thickness(0, 0, 10, 0) });
            
            var hexTextBox = new TextBox 
            { 
                Width = 100, 
                Text = SelectedColor.ToString(),
                VerticalAlignment = VerticalAlignment.Center
            };
            
            hexTextBox.TextChanged += (s, e) => {
                try
                {
                    var color = (Color)ColorConverter.ConvertFromString(hexTextBox.Text);
                    SelectedColor = color;
                    previewRect.Fill = new SolidColorBrush(SelectedColor);
                }
                catch
                {
                    // Invalid hex color, ignore
                }
            };
            
            hexPanel.Children.Add(hexTextBox);
            panel.Children.Add(hexPanel);

            return panel;
        }

        private Panel CreateSliderPanel(string label, byte initialValue, System.Action<double> onValueChanged)
        {
            var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 5) };
            
            var labelBlock = new TextBlock 
            { 
                Text = label, 
                Width = 50, 
                VerticalAlignment = VerticalAlignment.Center 
            };
            panel.Children.Add(labelBlock);

            var slider = new Slider
            {
                Minimum = 0,
                Maximum = 255,
                Value = initialValue,
                Width = 200,
                VerticalAlignment = VerticalAlignment.Center
            };
            
            slider.ValueChanged += (s, e) => onValueChanged(e.NewValue);
            panel.Children.Add(slider);

            var valueBlock = new TextBlock
            {
                Text = initialValue.ToString(),
                Width = 30,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 0, 0)
            };
            
            slider.ValueChanged += (s, e) => valueBlock.Text = ((int)e.NewValue).ToString();
            panel.Children.Add(valueBlock);

            return panel;
        }

        private Panel CreateButtonPanel()
        {
            var panel = new StackPanel 
            { 
                Orientation = Orientation.Horizontal, 
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(20)
            };

            var okButton = new Button
            {
                Content = "OK",
                Width = 75,
                Height = 25,
                Margin = new Thickness(0, 0, 10, 0),
                IsDefault = true
            };
            okButton.Click += (s, e) => { DialogResult = true; Close(); };
            panel.Children.Add(okButton);

            var cancelButton = new Button
            {
                Content = "Cancel",
                Width = 75,
                Height = 25,
                IsCancel = true
            };
            cancelButton.Click += (s, e) => { DialogResult = false; Close(); };
            panel.Children.Add(cancelButton);

            return panel;
        }

        private void SetupColorPicker()
        {
            // Color picker setup is handled in CreateColorPanel
        }
    }
}
