{"Name": "Default Theme", "Description": "Default application theme", "IsDarkTheme": false, "CreatedDate": "2025-09-24T11:01:43.6212493+07:00", "LastModified": "2025-09-24T11:01:43.6220471+07:00", "Colors": {"PrimaryBackground": {"Name": "Primary Background", "HexValue": "#FFFFFFFF", "Description": "Main background color", "Color": "#FFFFFFFF", "Brush": "#FFFFFFFF"}, "SecondaryBackground": {"Name": "Secondary Background", "HexValue": "#FFF5F5F5", "Description": "Secondary background color", "Color": "#FFF5F5F5", "Brush": "#FFF5F5F5"}, "AccentBackground": {"Name": "Accent Background", "HexValue": "#FFE3F2FD", "Description": "Accent background color", "Color": "#FFE3F2FD", "Brush": "#FFE3F2FD"}, "PrimaryText": {"Name": "Primary Text", "HexValue": "#FF212121", "Description": "Main text color", "Color": "#FF212121", "Brush": "#FF212121"}, "SecondaryText": {"Name": "Secondary Text", "HexValue": "#FF757575", "Description": "Secondary text color", "Color": "#FF757575", "Brush": "#FF757575"}, "AccentText": {"Name": "Accent Text", "HexValue": "#FF1976D2", "Description": "Accent text color", "Color": "#FF1976D2", "Brush": "#FF1976D2"}, "PrimaryBorder": {"Name": "Primary Border", "HexValue": "#FFE0E0E0", "Description": "Main border color", "Color": "#FFE0E0E0", "Brush": "#FFE0E0E0"}, "SecondaryBorder": {"Name": "Secondary Border", "HexValue": "#FFBDBDBD", "Description": "Secondary border color", "Color": "#FFBDBDBD", "Brush": "#FFBDBDBD"}, "AccentBorder": {"Name": "Accent Border", "HexValue": "#FF1976D2", "Description": "Accent border color", "Color": "#FF1976D2", "Brush": "#FF1976D2"}, "ButtonBackground": {"Name": "<PERSON>ton Background", "HexValue": "#FF2196F3", "Description": "Button background color", "Color": "#FF2196F3", "Brush": "#FF2196F3"}, "ButtonHover": {"Name": "Button Hover", "HexValue": "#FF1976D2", "Description": "Button hover color", "Color": "#FF1976D2", "Brush": "#FF1976D2"}, "ButtonPressed": {"Name": "Button Pressed", "HexValue": "#FF0D47A1", "Description": "Button pressed color", "Color": "#FF0D47A1", "Brush": "#FF0D47A1"}, "ButtonText": {"Name": "Button Text", "HexValue": "#FFFFFFFF", "Description": "Button text color", "Color": "#FFFFFFFF", "Brush": "#FFFFFFFF"}, "MenuBackground": {"Name": "<PERSON>u <PERSON>", "HexValue": "#FFFAFAFA", "Description": "Menu background color", "Color": "#FFFAFAFA", "Brush": "#FFFAFAFA"}, "MenuHover": {"Name": "<PERSON><PERSON>", "HexValue": "#FFE0E0E0", "Description": "Menu hover color", "Color": "#FFE0E0E0", "Brush": "#FFE0E0E0"}, "MenuSelected": {"Name": "<PERSON>u Selected", "HexValue": "#FFBBDEFB", "Description": "<PERSON><PERSON> selected color", "Color": "#FFBBDEFB", "Brush": "#FFBBDEFB"}, "SuccessColor": {"Name": "Success Color", "HexValue": "#FF4CAF50", "Description": "Success status color", "Color": "#FF4CAF50", "Brush": "#FF4CAF50"}, "WarningColor": {"Name": "Warning Color", "HexValue": "#FFFF9800", "Description": "Warning status color", "Color": "#FFFF9800", "Brush": "#FFFF9800"}, "ErrorColor": {"Name": "Error Color", "HexValue": "#FFF44336", "Description": "Error status color", "Color": "#FFF44336", "Brush": "#FFF44336"}, "InfoColor": {"Name": "Info Color", "HexValue": "#FF2196F3", "Description": "Info status color", "Color": "#FF2196F3", "Brush": "#FF2196F3"}}}