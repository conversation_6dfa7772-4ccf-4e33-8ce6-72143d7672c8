using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace AITS3D_PRO.Views.Pages
{
    /// <summary>
    /// Interaction logic for ModulePage.xaml
    /// </summary>
    public partial class ModulePage : UserControl
    {
        public ModulePage()
        {
            InitializeComponent();
        }
        private void Toolbox_PreviewMouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton != MouseButtonState.Pressed) return;
            if (sender is ListBox lb && lb.SelectedItem is ListBoxItem item)
            {
                var data = new DataObject();
                data.SetData("NodeType", item.Tag?.ToString());
                DragDrop.DoDragDrop(lb, data, DragDropEffects.Copy);
            }
        }

    }
}
