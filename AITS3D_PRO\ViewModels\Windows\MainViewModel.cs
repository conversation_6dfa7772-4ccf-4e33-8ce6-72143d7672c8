﻿using AITS3D_PRO.Models;
using AITS3D_PRO.Views.Pages;
using AITS3D_PRO.ViewModels.Pages;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Runtime.CompilerServices;
using System.Windows.Controls;
using System.Windows.Input;


namespace AITS3D_PRO.ViewModels.Windows
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private UserControl _currentPage = null!;
        private readonly Dictionary<PageType, UserControl> _pages;
        private readonly Dictionary<PageType, object> _viewModels;

        public MainViewModel()
        {
            // Khởi tạo dictionary để lưu trữ các pages và viewmodels
            _pages = new Dictionary<PageType, UserControl>();
            _viewModels = new Dictionary<PageType, object>();

            // Khởi tạo command
            NavigateCommand = new RelayCommand(Navigate);

            // Tạo các pages và viewmodels
            InitializePages();

            // Đặt trang mặc định là Home
            CurrentPage = _pages[PageType.Home];
        }

        public UserControl CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged();
            }
        }

        public ICommand NavigateCommand { get; }

        private void InitializePages()
        {
            // Tạo ViewModels
            _viewModels[PageType.Home] = new HomeViewModel();
            _viewModels[PageType.Dashboard] = new DashboardViewModel();
            _viewModels[PageType.Events] = new EventsViewModel();
            _viewModels[PageType.Processing] = new ProcessingViewModel();
            _viewModels[PageType.Module] = new ModuleViewModel();
            _viewModels[PageType.ColorThemeManager] = new ColorThemeManagerViewModel();

            // Tạo Pages và gán DataContext
            var homePage = new HomePage();
            homePage.DataContext = _viewModels[PageType.Home];
            _pages[PageType.Home] = homePage;

            var dashboardPage = new DashboardPage();
            dashboardPage.DataContext = _viewModels[PageType.Dashboard];
            _pages[PageType.Dashboard] = dashboardPage;

            var eventPage = new EventPage();
            eventPage.DataContext = _viewModels[PageType.Events];
            _pages[PageType.Events] = eventPage;

            var processingPage = new ProcessingPage();
            processingPage.DataContext = _viewModels[PageType.Processing];
            _pages[PageType.Processing] = processingPage;

            var modulePage = new ModulePage();
            modulePage.DataContext = _viewModels[PageType.Module];
            _pages[PageType.Module] = modulePage;

            var colorThemeManagerPage = new ColorThemeManagerPage();
            colorThemeManagerPage.DataContext = _viewModels[PageType.ColorThemeManager];
            _pages[PageType.ColorThemeManager] = colorThemeManagerPage;
        }

        private void Navigate(object? parameter)
        {
            if (parameter is PageType pageType && _pages.ContainsKey(pageType))
            {
                CurrentPage = _pages[pageType];
            }
            else if (parameter is string pageString)
            {
                // Xử lý trường hợp parameter là string (như "Home")
                if (System.Enum.TryParse<PageType>(pageString, out PageType parsedPageType) &&
                    _pages.ContainsKey(parsedPageType))
                {
                    CurrentPage = _pages[parsedPageType];
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}