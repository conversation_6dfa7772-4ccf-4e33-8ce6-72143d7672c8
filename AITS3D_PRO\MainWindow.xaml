﻿<Window x:Class="AITS3D_PRO.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:Icon="http://metro.mahapps.com/winfx/xaml/iconpacks"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:AITS3D_PRO.ViewModels.Windows"
        xmlns:model="clr-namespace:AITS3D_PRO.Models"
        xmlns:local="clr-namespace:AITS3D_PRO"
        mc:Ignorable="d"
        Title="AITS 3D PRO" Height="800" Width="1080"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        Background="Transparent">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolTovis"/>
    </Window.Resources>



    <Border Background="#eff2f7" MouseDown="Border_MouseDown" MouseLeftButtonDown="Border_MouseLeftButtonDown" >
        <Grid x:Name="MainGrid">
            <Grid.ColumnDefinitions>
                <ColumnDefinition x:Name="SidebarIcon" Width="0"/>
                <ColumnDefinition x:Name="SidebarFullMenu" Width="200"/>
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <Button Style="{StaticResource  topButton}"  HorizontalAlignment="Center" Margin="0 10 10 30"  Click="MenuButton_Click">
                    <Icon:PackIconMaterial Kind="Menu" Width="20" Height="20"/>
                </Button>

                <Border Grid.Row="1" Background="Brown" CornerRadius="0 20 0 0">
                    <DockPanel>
                        <Border Width="50" Height="50" Padding="3" Background="White" CornerRadius="100"
                                DockPanel.Dock="Top" HorizontalAlignment="Center" Margin="5 5 5 0">
                            <Ellipse Stroke="White" StrokeThickness="2">
                                <Ellipse.Fill>
                                    <ImageBrush ImageSource="src/image/Logo.png" Stretch="UniformToFill"/>
                                </Ellipse.Fill>
                            </Ellipse>
                        </Border>

                        <!--Menu Text Titles-->
                        <TextBlock Text="AITS" FontSize="16" FontWeight="SemiBold" Foreground="White" Margin="0 10 0 0"
                                    DockPanel.Dock="Top" HorizontalAlignment="Center"/>
                        <TextBlock Text="V1.0" FontSize="14" Foreground="White" Margin="0 0 0 0" DockPanel.Dock="Top" HorizontalAlignment="Center"/>

                        <!--Menu Separator-->
                        <Separator Margin="5" DockPanel.Dock="Top" Background="White" Height="2"/>

                        <!--Menu Buttons-->
                        <StackPanel DockPanel.Dock="Top" HorizontalAlignment="Center">
                            <!-- Home -->
                            <Button Style="{StaticResource menuButton}" x:Name="HomeButton"
                                    Background="Transparent" BorderThickness="0"
                                    HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                    Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Home}">
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Icon:PackIconMaterial Kind="HomeVariant" Style="{StaticResource menuIcon}"/>
                                    <TextBlock Text="Home" HorizontalAlignment="Center" Margin="0,5,0,0" MinHeight="20" FontSize="14">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsMouseOver, ElementName=HomeButton}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </StackPanel>
                            </Button>

                            <!-- Dashboard -->
                            <Button Style="{StaticResource menuButton}" x:Name="DashboardButton"
                                    Background="Transparent" BorderThickness="0"
                                    HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                    Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Dashboard}">
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Icon:PackIconMaterial Kind="MonitorDashboard" Style="{StaticResource menuIcon}"/>
                                    <TextBlock Text="Dashboard" HorizontalAlignment="Center" Margin="0,5,0,0" MinHeight="20" FontSize="14">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsMouseOver, ElementName=DashboardButton}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </StackPanel>
                            </Button>

                            <!-- Events -->
                            <Button Style="{StaticResource menuButton}" x:Name="EventsButton"
                                    Background="Transparent" BorderThickness="0"
                                    HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                    Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Events}">
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Icon:PackIconMaterial Kind="RunFast" Style="{StaticResource menuIcon}"/>
                                    <TextBlock Text="Events" HorizontalAlignment="Center" Margin="0,5,0,0" MinHeight="20" FontSize="14">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsMouseOver, ElementName=EventsButton}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </StackPanel>
                            </Button>

                            <!-- Processing -->
                            <Button Style="{StaticResource menuButton}" x:Name="ProcessingButton"
                                    Background="Transparent" BorderThickness="0"
                                    HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                    Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Processing}">
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Icon:PackIconMaterial Kind="CodeBraces" Style="{StaticResource menuIcon}"/>
                                    <TextBlock Text="Processing" HorizontalAlignment="Center" Margin="0,5,0,0" MinHeight="20" FontSize="14">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsMouseOver, ElementName=ProcessingButton}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </StackPanel>
                            </Button>

                            <!-- Module -->
                            <Button Style="{StaticResource menuButton}" x:Name="ModuleButton"
                                    Background="Transparent" BorderThickness="0"
                                    HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                    Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Module}">
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Icon:PackIconMaterial Kind="Atom" Style="{StaticResource menuIcon}"/>
                                    <TextBlock Text="Module" HorizontalAlignment="Center" Margin="0,5,0,0" MinHeight="20" FontSize="14">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsMouseOver, ElementName=ModuleButton}" Value="True">
                                                        <Setter Property="Visibility" Value="Visible"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- Loguot -->
                        <Button Style="{StaticResource menuButton}" x:Name="LogoutButton"
                                Background="Transparent" BorderThickness="0"
                                HorizontalContentAlignment="Center" VerticalContentAlignment="Center" DockPanel.Dock="Bottom" VerticalAlignment="Bottom" Margin="0 0 0 10">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <Icon:PackIconMaterial Kind="Logout" Style="{StaticResource menuIcon}"/>
                                <TextBlock Text="Logout" HorizontalAlignment="Center" Margin="0,5,0,0" MinHeight="20" FontSize="14">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsMouseOver, ElementName=LogoutButton}" Value="True">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>
                        </Button>


                    </DockPanel>
                </Border>

            </Grid>

            <!--Left Menu-->
            <Grid Grid.Column="1">
               
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Button Style="{StaticResource  topButton}"  HorizontalAlignment="Right" Margin="0 10 10 30"  Click="MenuButton_Click">
                        <Icon:PackIconMaterial Kind="Menu" Width="20" Height="20"/>
                    </Button>
                    
                    <Border Grid.Row="1" Background="Brown" CornerRadius="0 50 0 0">
                        <DockPanel>
                            <Border Width="100" Height="100" Padding="3" Background="White" CornerRadius="100"
                                        DockPanel.Dock="Top" HorizontalAlignment="Left" Margin="30 -30 0 0">
                                <Ellipse Stroke="White" StrokeThickness="2">
                                    <Ellipse.Fill>
                                        <ImageBrush ImageSource="src/image/Logo.png" Stretch="UniformToFill"/>
                                    </Ellipse.Fill>
                                </Ellipse>
                            </Border>

                            <!--Menu Text Titles-->
                            <TextBlock Text="AITS 3D PRO" FontSize="16" FontWeight="SemiBold" Foreground="White" Margin="20 10 0 0"
                                        DockPanel.Dock="Top"/>
                            <TextBlock Text="Version 1.0" FontSize="14" Foreground="White" Margin="20 0 0 0" DockPanel.Dock="Top"/>

                            <!--Menu Separator-->
                            <Separator Margin="5" DockPanel.Dock="Top" Background="White" Height="2"/>

                            <!--Menu Buttons-->
                            <StackPanel DockPanel.Dock="Top">
                                <Button Style="{StaticResource menuFullButton}" Command="{Binding NavigateCommand}"
                                        CommandParameter="{x:Static model:PageType.Home}">
                                    <StackPanel Orientation="Horizontal">
                                        <Icon:PackIconMaterial Kind="HomeVariant" Style="{StaticResource menuFullIcon}"/>
                                        <TextBlock Text="Home"/>
                                    </StackPanel>
                                </Button>

                                <Button Style="{StaticResource menuFullButton}" x:Name="DashboardButton2"
                                        Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Dashboard}">
                                    <StackPanel Orientation="Horizontal">
                                        <Icon:PackIconMaterial Kind="MonitorDashboard" Style="{StaticResource menuFullIcon}"/>
                                        <TextBlock Text="Dashboard"/>
                                    </StackPanel>
                                </Button>

                                <Button Style="{StaticResource menuFullButton}" x:Name="EventsButton2"
                                        Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Events}">
                                    <StackPanel Orientation="Horizontal">
                                        <Icon:PackIconMaterial Kind="RunFast" Style="{StaticResource menuFullIcon}"/>
                                        <TextBlock Text="Events"/>
                                    </StackPanel>
                                </Button>

                                <Button Style="{StaticResource menuFullButton}" x:Name="ProcessingButton2"
                                        Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Processing}">
                                    <StackPanel Orientation="Horizontal">
                                        <Icon:PackIconMaterial Kind="CodeBraces" Style="{StaticResource menuFullIcon}"/>
                                        <TextBlock Text="Processing"/>
                                    </StackPanel>
                                </Button>

                                <Button Style="{StaticResource menuFullButton}" x:Name="ModuleButton2"
                                        Command="{Binding NavigateCommand}" CommandParameter="{x:Static model:PageType.Module}">
                                    <StackPanel Orientation="Horizontal">
                                        <Icon:PackIconMaterial Kind="Atom" Style="{StaticResource menuFullIcon}"/>
                                        <TextBlock Text="Module"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>

                            <Button Style="{StaticResource menuFullButton}" x:Name="LogoutButton2" VerticalAlignment="Bottom" DockPanel.Dock="Bottom" Margin="15 0 15 20">
                                <StackPanel Orientation="Horizontal">
                                    <Icon:PackIconMaterial Kind="Logout" Style="{StaticResource menuFullIcon}"/>
                                    <TextBlock Text="Logout"/>
                                </StackPanel>
                            </Button>


                        </DockPanel>
                    </Border>
                </Grid>

            </Grid>

            <!--Main Section-->
            <Grid x:Name="MainSection" Grid.Column="2" Margin="20 0 10 0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="60" />
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <!--Search Texbox-->
                    <Grid Width="300" HorizontalAlignment="Left">
                        <TextBox x:Name="txtSearch" Style="{StaticResource textboxSearch}"/>

                        <TextBlock Text="Search Here..." FontSize="14" Foreground="#b0b9c6" 
                                   VerticalAlignment="Center" Margin="20 0 0 0" HorizontalAlignment="Left"
                                   Visibility="{Binding ElementName=txtSearch, Path=Text.IsEmpty,Converter={StaticResource BoolTovis}}"
                                   IsHitTestVisible="False" Panel.ZIndex="1"/>

                        <Icon:PackIconMaterial Kind="Magnify" Width="20" Height="20" VerticalAlignment="Center"
                           Margin="0 0 15 0" Foreground="#b0b9c6" HorizontalAlignment="Right"/>
                    </Grid>

                    <!--Top Buttons-->
                    <Button Style="{StaticResource  topButton}"  HorizontalAlignment="Right" Margin="0 0 20 0">
                        <Icon:PackIconMaterial Kind="Cog" Width="20" Height="20"/>
                    </Button>
                    <Button Style="{StaticResource  topButton}"  HorizontalAlignment="Right" Margin="0 0 60 0">
                        <Icon:PackIconMaterial Kind="Bell" Width="20" Height="20"/>
                    </Button>

                </Grid>
                
                <Grid Grid.Row="1" Margin="0 0 10 0">
                    <!--Content Area for Pages-->
                    <ContentControl Content="{Binding CurrentPage}"
                    Margin="0"/>
                </Grid>

                <Grid Grid.Row="2" Margin="0 10 10 0">
                    <Border Background="PaleGoldenrod"
                            Margin="0 0 0 5"
                            Padding="4"
                            BorderBrush="Black"
                            BorderThickness="2"
                            CornerRadius="20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- Cột 0: Text -->
                            <Grid Grid.Column="0">
                                <TextBlock Text="AITS 3D PRO" Grid.Row="1" FontSize="20" FontWeight="SemiBold"
                           Foreground="#2c3e50" Margin="0"
                           HorizontalAlignment="Left" VerticalAlignment="Top"/>
                            </Grid>

                            <!-- Cột 1: Button -->
                            <Grid Grid.Column="1">
                                <Button Style="{StaticResource terminalButton}" HorizontalAlignment="Center" Margin="0" 
                                        Click="TerminalButton_Click" VerticalAlignment="Top">
                                    <Icon:PackIconMaterial Kind="HelpCircle" Width="20" Height="20"/>
                                </Button>
                            </Grid>
                        </Grid>
                    </Border>
                </Grid>

            </Grid>
            

        </Grid>
    </Border>
    
    
</Window>
