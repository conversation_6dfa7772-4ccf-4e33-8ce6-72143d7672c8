<UserControl x:Class="AITS3D_PRO.Views.Pages.ThemeDemoPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AITS3D_PRO.Views.Pages"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid Background="{DynamicResource PrimaryBackgroundBrush}">
        <ScrollViewer Margin="20">
            <StackPanel>
                <!-- Header -->
                <TextBlock Text="Theme Demo Page" FontSize="24" FontWeight="Bold" 
                          Foreground="{DynamicResource PrimaryTextBrush}" 
                          HorizontalAlignment="Center" Margin="0,0,0,20"/>

                <!-- Quick Theme Switcher -->
                <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                       BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                       BorderThickness="1" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Quick Theme Switcher" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,0,0,10"/>
                        <StackPanel Orientation="Horizontal">
                            <Button Content="Light Theme" Margin="0,0,10,0" Padding="10,5" 
                                   Background="{DynamicResource ButtonBackgroundBrush}"
                                   Foreground="{DynamicResource ButtonTextBrush}"/>
                            <Button Content="Dark Theme" Margin="0,0,10,0" Padding="10,5"
                                   Background="{DynamicResource ButtonBackgroundBrush}"
                                   Foreground="{DynamicResource ButtonTextBrush}"/>
                            <Button Content="Blue Theme" Padding="10,5"
                                   Background="{DynamicResource ButtonBackgroundBrush}"
                                   Foreground="{DynamicResource ButtonTextBrush}"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Text Samples -->
                <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                       BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                       BorderThickness="1" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Text Samples" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="Primary Text - This is the main text color used throughout the application." 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,5"/>
                        
                        <TextBlock Text="Secondary Text - This is used for less important information and descriptions." 
                                  Foreground="{DynamicResource SecondaryTextBrush}" Margin="0,5"/>
                        
                        <TextBlock Text="Accent Text - This is used for links, highlights, and important information." 
                                  Foreground="{DynamicResource AccentTextBrush}" Margin="0,5"/>
                    </StackPanel>
                </Border>

                <!-- Button Samples -->
                <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                       BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                       BorderThickness="1" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Button Samples" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,0,0,10"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <Button Content="Primary Button" Margin="0,0,10,0" Padding="15,8"
                                   Background="{DynamicResource ButtonBackgroundBrush}"
                                   Foreground="{DynamicResource ButtonTextBrush}"/>
                            
                            <Button Content="Secondary Button" Margin="0,0,10,0" Padding="15,8"
                                   Background="{DynamicResource SecondaryBackgroundBrush}"
                                   Foreground="{DynamicResource PrimaryTextBrush}"
                                   BorderBrush="{DynamicResource PrimaryBorderBrush}"
                                   BorderThickness="1"/>
                            
                            <Button Content="Accent Button" Padding="15,8"
                                   Background="{DynamicResource AccentBackgroundBrush}"
                                   Foreground="{DynamicResource AccentTextBrush}"
                                   BorderBrush="{DynamicResource AccentBorderBrush}"
                                   BorderThickness="1"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Input Controls -->
                <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                       BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                       BorderThickness="1" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Input Controls" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Text Input:" 
                                      Foreground="{DynamicResource PrimaryTextBrush}" 
                                      VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <TextBox Grid.Row="0" Grid.Column="1" Text="Sample text input" 
                                    Background="{DynamicResource PrimaryBackgroundBrush}"
                                    Foreground="{DynamicResource PrimaryTextBrush}"
                                    BorderBrush="{DynamicResource PrimaryBorderBrush}"
                                    Margin="0,5" Padding="8"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Checkbox:" 
                                      Foreground="{DynamicResource PrimaryTextBrush}" 
                                      VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <CheckBox Grid.Row="1" Grid.Column="1" Content="Sample checkbox" 
                                     Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="ComboBox:" 
                                      Foreground="{DynamicResource PrimaryTextBrush}" 
                                      VerticalAlignment="Center" Margin="0,5,10,5"/>
                            <ComboBox Grid.Row="2" Grid.Column="1" Margin="0,5" Padding="8"
                                     Background="{DynamicResource PrimaryBackgroundBrush}"
                                     Foreground="{DynamicResource PrimaryTextBrush}"
                                     BorderBrush="{DynamicResource PrimaryBorderBrush}">
                                <ComboBoxItem Content="Option 1"/>
                                <ComboBoxItem Content="Option 2"/>
                                <ComboBoxItem Content="Option 3"/>
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Status Colors -->
                <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                       BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                       BorderThickness="1" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Status Colors" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,0,0,10"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <Border Background="{DynamicResource SuccessColorBrush}" 
                                   CornerRadius="4" Padding="10,5" Margin="0,0,10,0">
                                <TextBlock Text="Success" Foreground="White" FontWeight="Bold"/>
                            </Border>
                            
                            <Border Background="{DynamicResource WarningColorBrush}" 
                                   CornerRadius="4" Padding="10,5" Margin="0,0,10,0">
                                <TextBlock Text="Warning" Foreground="White" FontWeight="Bold"/>
                            </Border>
                            
                            <Border Background="{DynamicResource ErrorColorBrush}" 
                                   CornerRadius="4" Padding="10,5" Margin="0,0,10,0">
                                <TextBlock Text="Error" Foreground="White" FontWeight="Bold"/>
                            </Border>
                            
                            <Border Background="{DynamicResource InfoColorBrush}" 
                                   CornerRadius="4" Padding="10,5">
                                <TextBlock Text="Info" Foreground="White" FontWeight="Bold"/>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Menu Sample -->
                <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                       BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                       BorderThickness="1" CornerRadius="8" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Menu Sample" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,0,0,10"/>
                        
                        <ListBox Background="{DynamicResource MenuBackgroundBrush}" 
                                BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                                BorderThickness="1" Height="120">
                            <ListBoxItem Content="Menu Item 1" 
                                        Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <ListBoxItem Content="Menu Item 2" 
                                        Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <ListBoxItem Content="Menu Item 3" IsSelected="True"
                                        Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <ListBoxItem Content="Menu Item 4" 
                                        Foreground="{DynamicResource PrimaryTextBrush}"/>
                        </ListBox>
                    </StackPanel>
                </Border>

                <!-- Color Palette Preview -->
                <Border Background="{DynamicResource SecondaryBackgroundBrush}" 
                       BorderBrush="{DynamicResource PrimaryBorderBrush}" 
                       BorderThickness="1" CornerRadius="8" Padding="15">
                    <StackPanel>
                        <TextBlock Text="Current Color Palette" FontSize="16" FontWeight="Bold" 
                                  Foreground="{DynamicResource PrimaryTextBrush}" Margin="0,0,0,10"/>
                        
                        <UniformGrid Columns="4" Rows="4">
                            <!-- Background Colors -->
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource PrimaryBackgroundBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Primary BG" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource SecondaryBackgroundBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Secondary BG" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource AccentBackgroundBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Accent BG" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource MenuBackgroundBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Menu BG" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>

                            <!-- Text Colors -->
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource PrimaryTextBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Primary Text" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource SecondaryTextBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Secondary Text" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource AccentTextBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Accent Text" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource ButtonTextBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Button Text" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>

                            <!-- Button Colors -->
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource ButtonBackgroundBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Button BG" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource ButtonHoverBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Button Hover" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource ButtonPressedBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Button Pressed" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource MenuSelectedBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Menu Selected" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>

                            <!-- Status Colors -->
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource SuccessColorBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Success" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource WarningColorBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Warning" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource ErrorColorBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Error" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                            
                            <StackPanel Margin="5">
                                <Rectangle Fill="{DynamicResource InfoColorBrush}" 
                                          Width="60" Height="40" Stroke="{DynamicResource PrimaryBorderBrush}"/>
                                <TextBlock Text="Info" FontSize="10" HorizontalAlignment="Center" 
                                          Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                        </UniformGrid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
