using AITS3D_PRO.Models;
using AITS3D_PRO.Services;
using AITS3D_PRO.ViewModels;
using Microsoft.Win32;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;

namespace AITS3D_PRO.ViewModels.Pages
{
    public class ColorThemeManagerViewModel : INotifyPropertyChanged
    {
        private ColorTheme _currentTheme;
        private ColorTheme _selectedTheme;
        private ColorProperty _selectedColorProperty;
        private string _newThemeName;
        private string _newThemeDescription;
        private bool _isEditMode;
        private string _searchText;

        public ObservableCollection<ColorTheme> AvailableThemes { get; set; }
        public ObservableCollection<ColorProperty> FilteredColors { get; set; }

        public ColorTheme CurrentTheme
        {
            get => _currentTheme;
            set
            {
                _currentTheme = value;
                OnPropertyChanged();
                UpdateFilteredColors();
            }
        }

        public ColorTheme SelectedTheme
        {
            get => _selectedTheme;
            set
            {
                _selectedTheme = value;
                OnPropertyChanged();
                LoadThemeCommand?.RaiseCanExecuteChanged();
                DeleteThemeCommand?.RaiseCanExecuteChanged();
                ExportJsonCommand?.RaiseCanExecuteChanged();
                ExportCsvCommand?.RaiseCanExecuteChanged();
            }
        }

        public ColorProperty SelectedColorProperty
        {
            get => _selectedColorProperty;
            set
            {
                _selectedColorProperty = value;
                OnPropertyChanged();
            }
        }

        public string NewThemeName
        {
            get => _newThemeName;
            set
            {
                _newThemeName = value;
                OnPropertyChanged();
                SaveThemeCommand?.RaiseCanExecuteChanged();
            }
        }

        public string NewThemeDescription
        {
            get => _newThemeDescription;
            set
            {
                _newThemeDescription = value;
                OnPropertyChanged();
            }
        }

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                _isEditMode = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                OnPropertyChanged();
                UpdateFilteredColors();
            }
        }

        // Commands
        public RelayCommand LoadThemeCommand { get; set; }
        public RelayCommand SaveThemeCommand { get; set; }
        public RelayCommand DeleteThemeCommand { get; set; }
        public RelayCommand NewThemeCommand { get; set; }
        public RelayCommand EditThemeCommand { get; set; }
        public RelayCommand CancelEditCommand { get; set; }
        public RelayCommand ApplyThemeCommand { get; set; }
        public RelayCommand ResetToDefaultCommand { get; set; }
        public RelayCommand ExportJsonCommand { get; set; }
        public RelayCommand ExportCsvCommand { get; set; }
        public RelayCommand ImportJsonCommand { get; set; }
        public RelayCommand ImportCsvCommand { get; set; }
        public RelayCommand LoadPredefinedThemesCommand { get; set; }

        public ColorThemeManagerViewModel()
        {
            AvailableThemes = new ObservableCollection<ColorTheme>();
            FilteredColors = new ObservableCollection<ColorProperty>();

            InitializeCommands();
            LoadThemes();
            LoadCurrentTheme();
        }

        private void InitializeCommands()
        {
            LoadThemeCommand = new RelayCommand(LoadTheme, () => SelectedTheme != null);
            SaveThemeCommand = new RelayCommand(SaveTheme, () => !string.IsNullOrWhiteSpace(NewThemeName));
            DeleteThemeCommand = new RelayCommand(DeleteTheme, () => SelectedTheme != null);
            NewThemeCommand = new RelayCommand(NewTheme);
            EditThemeCommand = new RelayCommand(EditTheme);
            CancelEditCommand = new RelayCommand(CancelEdit);
            ApplyThemeCommand = new RelayCommand(ApplyTheme);
            ResetToDefaultCommand = new RelayCommand(ResetToDefault);
            ExportJsonCommand = new RelayCommand(ExportJson, () => SelectedTheme != null);
            ExportCsvCommand = new RelayCommand(ExportCsv, () => SelectedTheme != null);
            ImportJsonCommand = new RelayCommand(ImportJson);
            ImportCsvCommand = new RelayCommand(ImportCsv);
            LoadPredefinedThemesCommand = new RelayCommand(LoadPredefinedThemes);
        }

        private void LoadThemes()
        {
            AvailableThemes.Clear();
            var themes = ThemeService.Instance.GetAvailableThemes();
            foreach (var theme in themes)
            {
                AvailableThemes.Add(theme);
            }
        }

        private void LoadCurrentTheme()
        {
            CurrentTheme = ThemeService.Instance.CurrentTheme;
            if (CurrentTheme != null)
            {
                NewThemeName = CurrentTheme.Name;
                NewThemeDescription = CurrentTheme.Description;
            }
        }

        private void UpdateFilteredColors()
        {
            FilteredColors.Clear();

            if (CurrentTheme?.Colors == null) return;

            IEnumerable<ColorProperty> colors = CurrentTheme.Colors.Values;

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                colors = colors.Where(c =>
                    c.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    c.Description.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
            }

            foreach (var color in colors)
            {
                FilteredColors.Add(color);
            }
        }

        private void LoadTheme()
        {
            if (SelectedTheme == null) return;

            CurrentTheme = SelectedTheme;
            NewThemeName = CurrentTheme.Name;
            NewThemeDescription = CurrentTheme.Description;
            IsEditMode = false;
        }

        private void SaveTheme()
        {
            if (CurrentTheme == null || string.IsNullOrWhiteSpace(NewThemeName)) return;

            CurrentTheme.Name = NewThemeName;
            CurrentTheme.Description = NewThemeDescription ?? "";

            ThemeService.Instance.SaveTheme(CurrentTheme);
            LoadThemes();
            IsEditMode = false;

            MessageBox.Show($"Theme '{CurrentTheme.Name}' saved successfully!", "Theme Saved", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteTheme()
        {
            if (SelectedTheme == null) return;

            var result = MessageBox.Show($"Are you sure you want to delete the theme '{SelectedTheme.Name}'?", 
                "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ThemeService.Instance.DeleteTheme(SelectedTheme.Name);
                LoadThemes();
                SelectedTheme = null;
            }
        }

        private void NewTheme()
        {
            CurrentTheme = new ColorTheme
            {
                Name = "New Theme",
                Description = "Custom theme"
            };
            NewThemeName = CurrentTheme.Name;
            NewThemeDescription = CurrentTheme.Description;
            IsEditMode = true;
        }

        private void EditTheme()
        {
            IsEditMode = true;
        }

        private void CancelEdit()
        {
            IsEditMode = false;
            LoadCurrentTheme();
        }

        private void ApplyTheme()
        {
            if (CurrentTheme == null) return;

            ThemeService.Instance.ApplyTheme(CurrentTheme);
            MessageBox.Show("Theme applied successfully!", "Theme Applied", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetToDefault()
        {
            var result = MessageBox.Show("Are you sure you want to reset to default colors? This will lose any unsaved changes.", 
                "Confirm Reset", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                CurrentTheme = new ColorTheme
                {
                    Name = NewThemeName ?? "Default Theme",
                    Description = NewThemeDescription ?? "Default theme"
                };
                UpdateFilteredColors();
            }
        }

        private void ExportJson()
        {
            if (SelectedTheme == null) return;

            var dialog = new SaveFileDialog
            {
                Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                DefaultExt = "json",
                FileName = $"{SelectedTheme.Name}.json"
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    ThemeService.Instance.ExportThemeToJson(SelectedTheme, dialog.FileName);
                    MessageBox.Show("Theme exported successfully!", "Export Complete", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting theme: {ex.Message}", "Export Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ExportCsv()
        {
            if (SelectedTheme == null) return;

            var dialog = new SaveFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                DefaultExt = "csv",
                FileName = $"{SelectedTheme.Name}.csv"
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    ThemeService.Instance.ExportThemeToCsv(SelectedTheme, dialog.FileName);
                    MessageBox.Show("Theme exported successfully!", "Export Complete", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting theme: {ex.Message}", "Export Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ImportJson()
        {
            var dialog = new OpenFileDialog
            {
                Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*",
                DefaultExt = "json"
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var theme = ThemeService.Instance.ImportThemeFromJson(dialog.FileName);
                    if (theme != null)
                    {
                        CurrentTheme = theme;
                        NewThemeName = theme.Name;
                        NewThemeDescription = theme.Description;
                        MessageBox.Show("Theme imported successfully!", "Import Complete", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("Failed to import theme. Please check the file format.", "Import Error", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing theme: {ex.Message}", "Import Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ImportCsv()
        {
            var dialog = new OpenFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                DefaultExt = "csv"
            };

            if (dialog.ShowDialog() == true)
            {
                try
                {
                    var theme = ThemeService.Instance.ImportThemeFromCsv(dialog.FileName);
                    if (theme != null)
                    {
                        CurrentTheme = theme;
                        NewThemeName = theme.Name;
                        NewThemeDescription = theme.Description;
                        MessageBox.Show("Theme imported successfully!", "Import Complete", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("Failed to import theme. Please check the file format.", "Import Error", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing theme: {ex.Message}", "Import Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void LoadPredefinedThemes()
        {
            var predefinedThemes = ThemeService.Instance.GetPredefinedThemes();
            foreach (var theme in predefinedThemes)
            {
                ThemeService.Instance.SaveTheme(theme);
            }
            LoadThemes();
            MessageBox.Show("Predefined themes loaded successfully!", "Themes Loaded", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
