﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>src\image\1_2x-Copy.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="src\image\Logo.png" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="src\image\1_2x-Copy.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Extended.Wpf.Toolkit" Version="5.0.0" />
    <PackageReference Include="MahApps.Metro.IconPacks" Version="6.1.0" />
    <PackageReference Include="MaterialDesignColors" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes.MahApps" Version="5.2.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.4" />
    <PackageReference Include="NodeNetwork" Version="6.0.0" />
    <PackageReference Include="StringMath" Version="4.1.3" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="src\image\Logo.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>

</Project>
