# Hướng dẫn tích hợp Theme System vào MainWindow

## 1. Thêm menu item cho Theme Manager

### Cách 1: Thêm vào menu hiện tại
Trong file `MainWindow.xaml`, thêm một menu item hoặc button để navigate đến ColorThemeManager:

```xml
<!-- Thêm button vào menu sidebar -->
<Button Content="🎨 Theme Manager" 
        Command="{Binding NavigateCommand}" 
        CommandParameter="ColorThemeManager"
        Margin="5" Padding="10,8"
        Background="{DynamicResource ButtonBackgroundBrush}"
        Foreground="{DynamicResource ButtonTextBrush}"/>
```

### Cách 2: Thêm vào toolbar
```xml
<!-- Thêm vào toolbar -->
<Button Content="🎨" ToolTip="Theme Manager"
        Command="{Binding NavigateCommand}" 
        CommandParameter="ColorThemeManager"
        Width="40" Height="40"/>
```

### Cách 3: Thêm vào context menu
```xml
<!-- Thêm vào context menu -->
<ContextMenu>
    <MenuItem Header="Theme Manager" 
              Command="{Binding NavigateCommand}" 
              CommandParameter="ColorThemeManager"/>
</ContextMenu>
```

## 2. Cập nhật MainWindow để sử dụng theme colors

### Cập nhật MainWindow.xaml
Thay thế các màu hard-coded bằng theme colors:

```xml
<!-- Thay vì -->
<Grid Background="#FFFFFF">

<!-- Sử dụng -->
<Grid Background="{DynamicResource PrimaryBackgroundBrush}">
```

```xml
<!-- Thay vì -->
<TextBlock Foreground="#000000">

<!-- Sử dụng -->
<TextBlock Foreground="{DynamicResource PrimaryTextBrush}">
```

### Ví dụ cập nhật sidebar
```xml
<!-- Sidebar với theme colors -->
<Border Background="{DynamicResource MenuBackgroundBrush}" 
        BorderBrush="{DynamicResource PrimaryBorderBrush}" 
        BorderThickness="0,0,1,0">
    <StackPanel>
        <!-- Menu items -->
        <Button Content="🏠 Home" 
                Background="{DynamicResource MenuBackgroundBrush}"
                Foreground="{DynamicResource PrimaryTextBrush}"
                Command="{Binding NavigateCommand}" 
                CommandParameter="Home">
            <Button.Style>
                <Style TargetType="Button">
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource MenuHoverBrush}"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Button.Style>
        </Button>
        
        <!-- Thêm Theme Manager button -->
        <Button Content="🎨 Theme Manager" 
                Background="{DynamicResource MenuBackgroundBrush}"
                Foreground="{DynamicResource PrimaryTextBrush}"
                Command="{Binding NavigateCommand}" 
                CommandParameter="ColorThemeManager">
            <Button.Style>
                <Style TargetType="Button">
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource MenuHoverBrush}"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Button.Style>
        </Button>
    </StackPanel>
</Border>
```

## 3. Test Theme System

### Thêm vào MainWindow.xaml.cs
```csharp
public MainWindow()
{
    InitializeComponent();
    ViewModel = new MainViewModel();
    this.DataContext = ViewModel;
    
    // Test theme system (có thể xóa sau khi test xong)
    #if DEBUG
    TestThemeSystem.CreateSampleThemes();
    TestThemeSystem.RunTests();
    #endif
}
```

### Hoặc tạo menu item để test
```xml
<MenuItem Header="Debug">
    <MenuItem Header="Create Sample Themes" Click="CreateSampleThemes_Click"/>
    <MenuItem Header="Test Theme System" Click="TestThemeSystem_Click"/>
    <MenuItem Header="Print Theme Info" Click="PrintThemeInfo_Click"/>
</MenuItem>
```

```csharp
private void CreateSampleThemes_Click(object sender, RoutedEventArgs e)
{
    TestThemeSystem.CreateSampleThemes();
    MessageBox.Show("Sample themes created!");
}

private void TestThemeSystem_Click(object sender, RoutedEventArgs e)
{
    TestThemeSystem.RunTests();
    MessageBox.Show("Theme system tests completed!");
}

private void PrintThemeInfo_Click(object sender, RoutedEventArgs e)
{
    TestThemeSystem.PrintCurrentThemeInfo();
}
```

## 4. Cập nhật existing styles

### ButtonStyles.xaml
```xml
<Style x:Key="ModernButton" TargetType="Button">
    <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
    <Setter Property="Foreground" Value="{DynamicResource ButtonTextBrush}"/>
    <Setter Property="BorderBrush" Value="{DynamicResource AccentBorderBrush}"/>
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Background="{TemplateBinding Background}" 
                        BorderBrush="{TemplateBinding BorderBrush}" 
                        BorderThickness="1" CornerRadius="4">
                    <ContentPresenter HorizontalAlignment="Center" 
                                    VerticalAlignment="Center"/>
                </Border>
                <ControlTemplate.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{DynamicResource ButtonHoverBrush}"/>
                    </Trigger>
                    <Trigger Property="IsPressed" Value="True">
                        <Setter Property="Background" Value="{DynamicResource ButtonPressedBrush}"/>
                    </Trigger>
                </ControlTemplate.Triggers>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

### TextBoxStyle.xaml
```xml
<Style x:Key="ModernTextBox" TargetType="TextBox">
    <Setter Property="Background" Value="{DynamicResource PrimaryBackgroundBrush}"/>
    <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
    <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBorderBrush}"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="Padding" Value="8"/>
    <Style.Triggers>
        <Trigger Property="IsFocused" Value="True">
            <Setter Property="BorderBrush" Value="{DynamicResource AccentBorderBrush}"/>
        </Trigger>
    </Style.Triggers>
</Style>
```

## 5. Keyboard shortcuts (Optional)

### Thêm vào MainWindow.xaml
```xml
<Window.InputBindings>
    <KeyBinding Key="T" Modifiers="Ctrl+Shift" 
                Command="{Binding NavigateCommand}" 
                CommandParameter="ColorThemeManager"/>
</Window.InputBindings>
```

## 6. Settings integration (Optional)

### Lưu theme preference
```csharp
// Trong App.xaml.cs hoặc Settings class
public static void SaveThemePreference(string themeName)
{
    Properties.Settings.Default.SelectedTheme = themeName;
    Properties.Settings.Default.Save();
}

public static void LoadThemePreference()
{
    var themeName = Properties.Settings.Default.SelectedTheme;
    if (!string.IsNullOrEmpty(themeName))
    {
        var themes = ThemeService.Instance.GetAvailableThemes();
        var theme = themes.FirstOrDefault(t => t.Name == themeName);
        if (theme != null)
        {
            ThemeService.Instance.ApplyTheme(theme);
        }
    }
}
```

## 7. Quick theme switcher (Optional)

### Thêm vào toolbar hoặc status bar
```xml
<ComboBox ItemsSource="{Binding AvailableThemes}" 
          SelectedItem="{Binding SelectedTheme}"
          DisplayMemberPath="Name"
          Width="150" Margin="5">
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="SelectionChanged">
            <i:InvokeCommandAction Command="{Binding ApplySelectedThemeCommand}"/>
        </i:EventTrigger>
    </i:Interaction.Triggers>
</ComboBox>
```

## 8. Validation và Error Handling

### Thêm try-catch cho theme operations
```csharp
private void ApplyTheme_Click(object sender, RoutedEventArgs e)
{
    try
    {
        var selectedTheme = GetSelectedTheme();
        if (selectedTheme != null)
        {
            ThemeService.Instance.ApplyTheme(selectedTheme);
            ShowSuccessMessage("Theme applied successfully!");
        }
    }
    catch (Exception ex)
    {
        ShowErrorMessage($"Failed to apply theme: {ex.Message}");
    }
}
```

## 9. Performance considerations

### Lazy loading themes
```csharp
// Load themes only when needed
private ObservableCollection<ColorTheme> _availableThemes;
public ObservableCollection<ColorTheme> AvailableThemes
{
    get
    {
        if (_availableThemes == null)
        {
            _availableThemes = new ObservableCollection<ColorTheme>(
                ThemeService.Instance.GetAvailableThemes()
            );
        }
        return _availableThemes;
    }
}
```

## 10. Debugging tips

### Theme change logging
```csharp
// Trong App.xaml.cs
protected override void OnStartup(StartupEventArgs e)
{
    base.OnStartup(e);
    
    ThemeService.Instance.ThemeChanged += (sender, theme) =>
    {
        System.Diagnostics.Debug.WriteLine($"Theme changed to: {theme.Name}");
    };
}
```

### Resource debugging
```csharp
// Check if resource exists
private bool CheckResourceExists(string resourceKey)
{
    return Application.Current.Resources.Contains(resourceKey);
}
```

## Checklist tích hợp

- [ ] Thêm ColorThemeManager vào PageType enum
- [ ] Cập nhật MainViewModel để include ColorThemeManagerPage
- [ ] Thêm menu item/button để navigate đến Theme Manager
- [ ] Cập nhật MainWindow.xaml để sử dụng theme colors
- [ ] Cập nhật existing styles để sử dụng theme colors
- [ ] Test theme system với sample themes
- [ ] Thêm keyboard shortcuts (optional)
- [ ] Tích hợp với settings system (optional)
- [ ] Thêm quick theme switcher (optional)
- [ ] Test export/import functionality
- [ ] Verify theme persistence across app restarts
