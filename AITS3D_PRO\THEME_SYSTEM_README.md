# Color Theme Management System

Hệ thống quản lý theme màu sắc hoàn chỉnh cho ứng dụng WPF AITS3D_PRO.

## Tính năng chính

### 1. Quản lý Theme
- **Tạo theme mới**: Tạo và tùy chỉnh theme theo ý muốn
- **Lưu/Load theme**: Lưu theme vào file và load lại khi cần
- **Xóa theme**: Xóa các theme không cần thiết
- **Theme có sẵn**: Cung cấp các theme mặc định (Light, Dark, Blue)

### 2. Xuất/Nhập cấu hình
- **Xuất JSON**: Xuất theme ra file JSON để backup hoặc chia sẻ
- **Xuất CSV**: Xuất danh sách màu ra file CSV để chỉnh sửa bằng Excel
- **Nhập JSON**: Nhập theme từ file JSON
- **Nhập CSV**: <PERSON>h<PERSON><PERSON> màu từ file CSV

### 3. Chỉnh sửa màu sắc
- **Color Picker**: <PERSON>ia<PERSON> diện chọn màu trực quan với RGB sliders và hex input
- **Preview real-time**: Xem trước màu ngay khi chỉnh sửa
- **Tìm kiếm màu**: Tìm kiếm màu theo tên hoặc mô tả
- **Phân loại màu**: Màu được phân loại theo chức năng (Background, Text, Button, etc.)

## Cấu trúc hệ thống

### Models
- **ColorTheme.cs**: Model chính cho theme, chứa thông tin theme và danh sách màu
- **ColorProperty.cs**: Model cho từng màu, bao gồm tên, giá trị hex, mô tả

### Services
- **ThemeService.cs**: Service chính quản lý theme
  - Singleton pattern để đảm bảo consistency
  - Load/Save theme từ/ra file
  - Apply theme cho toàn bộ ứng dụng
  - Export/Import JSON/CSV

### ViewModels
- **ColorThemeManagerViewModel.cs**: ViewModel cho trang quản lý theme
  - Commands cho tất cả các thao tác
  - Data binding cho UI
  - Validation và error handling

### Views
- **ColorThemeManagerPage.xaml**: Giao diện chính quản lý theme
- **ColorPickerWindow**: Popup chọn màu
- **ThemeDemoPage.xaml**: Trang demo để test theme

### Resources
- **ThemeColors.xaml**: Resource dictionary chứa tất cả màu theme
- Tự động cập nhật khi theme thay đổi

## Cách sử dụng

### 1. Truy cập Theme Manager
```csharp
// Navigate đến ColorThemeManager page
NavigateCommand.Execute(PageType.ColorThemeManager);
```

### 2. Tạo theme mới
1. Click "New" trong panel bên trái
2. Nhập tên và mô tả cho theme
3. Chỉnh sửa màu sắc trong panel giữa
4. Click "Save Theme" để lưu

### 3. Chỉnh sửa màu
1. Click vào icon 🎨 bên cạnh màu muốn chỉnh sửa
2. Sử dụng RGB sliders hoặc nhập hex code
3. Click OK để áp dụng

### 4. Apply theme
1. Chọn theme trong danh sách
2. Click "Load Theme" để load vào editor
3. Click "Apply Theme" để áp dụng cho toàn bộ app

### 5. Export/Import
- **Export JSON**: Chọn theme → Click "Export JSON" → Chọn vị trí lưu
- **Export CSV**: Chọn theme → Click "Export CSV" → Chọn vị trí lưu
- **Import JSON**: Click "Import JSON" → Chọn file JSON
- **Import CSV**: Click "Import CSV" → Chọn file CSV

## Sử dụng trong code

### 1. Sử dụng theme colors trong XAML
```xml
<!-- Background -->
<Grid Background="{DynamicResource PrimaryBackgroundBrush}">

<!-- Text -->
<TextBlock Foreground="{DynamicResource PrimaryTextBrush}" />

<!-- Button -->
<Button Background="{DynamicResource ButtonBackgroundBrush}" 
        Foreground="{DynamicResource ButtonTextBrush}" />
```

### 2. Sử dụng trong C#
```csharp
// Get current theme
var currentTheme = ThemeService.Instance.CurrentTheme;

// Apply theme programmatically
ThemeService.Instance.ApplyTheme(myTheme);

// Listen for theme changes
ThemeService.Instance.ThemeChanged += (sender, theme) => {
    // Handle theme change
};
```

### 3. Thêm màu mới
```csharp
// Trong ColorTheme constructor hoặc khi tạo theme mới
Colors["MyCustomColor"] = new ColorProperty("My Custom Color", "#FF5722", "Description");
```

## Danh sách màu có sẵn

### Background Colors
- **PrimaryBackground**: Màu nền chính
- **SecondaryBackground**: Màu nền phụ
- **AccentBackground**: Màu nền nhấn mạnh
- **MenuBackground**: Màu nền menu

### Text Colors
- **PrimaryText**: Màu chữ chính
- **SecondaryText**: Màu chữ phụ
- **AccentText**: Màu chữ nhấn mạnh
- **ButtonText**: Màu chữ trên button

### Border Colors
- **PrimaryBorder**: Màu viền chính
- **SecondaryBorder**: Màu viền phụ
- **AccentBorder**: Màu viền nhấn mạnh

### Button Colors
- **ButtonBackground**: Màu nền button
- **ButtonHover**: Màu button khi hover
- **ButtonPressed**: Màu button khi nhấn

### Menu Colors
- **MenuHover**: Màu menu khi hover
- **MenuSelected**: Màu menu khi được chọn

### Status Colors
- **SuccessColor**: Màu thành công (xanh lá)
- **WarningColor**: Màu cảnh báo (vàng/cam)
- **ErrorColor**: Màu lỗi (đỏ)
- **InfoColor**: Màu thông tin (xanh dương)

## File structure
```
Themes/
├── current_theme.json          # Theme hiện tại
├── Light Theme.json           # Theme sáng
├── Dark Theme.json            # Theme tối
└── Blue Theme.json            # Theme xanh
```

## Lưu ý kỹ thuật

### 1. Dynamic Resources
- Sử dụng `DynamicResource` thay vì `StaticResource` để theme có thể thay đổi runtime
- Tất cả màu được define trong `ThemeColors.xaml`

### 2. Singleton Pattern
- `ThemeService` sử dụng singleton để đảm bảo consistency
- Chỉ có một instance duy nhất trong toàn bộ ứng dụng

### 3. Event-driven Updates
- Khi theme thay đổi, `ThemeChanged` event được fire
- UI tự động cập nhật nhờ `DynamicResource`

### 4. File Management
- Theme được lưu trong thư mục `Themes` trong application directory
- Tự động tạo thư mục nếu chưa tồn tại
- File name được sanitize để tránh lỗi

## Troubleshooting

### 1. Theme không apply
- Kiểm tra xem có sử dụng `DynamicResource` không
- Đảm bảo `ThemeColors.xaml` được include trong `App.xaml`

### 2. Màu không hiển thị đúng
- Kiểm tra tên màu có đúng không (case-sensitive)
- Đảm bảo màu đã được define trong theme

### 3. File không load được
- Kiểm tra quyền write/read trong thư mục application
- Đảm bảo file JSON format đúng

## Mở rộng

### 1. Thêm màu mới
1. Thêm vào `InitializeDefaultColors()` trong `ColorTheme.cs`
2. Thêm vào `ThemeColors.xaml`
3. Sử dụng trong UI

### 2. Thêm theme preset
1. Thêm vào `GetPredefinedThemes()` trong `ThemeService.cs`
2. Define các màu cho theme mới

### 3. Custom color picker
- Có thể thay thế `ColorPickerWindow` bằng color picker library khác
- Hoặc tích hợp với Material Design color picker
