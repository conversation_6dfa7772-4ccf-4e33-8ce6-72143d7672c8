﻿using AITS3D_PRO.Services;
using System.Configuration;
using System.Data;
using System.Windows;

namespace AITS3D_PRO
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Initialize Theme Service
            var themeService = ThemeService.Instance;

            // Apply current theme on startup
            if (themeService.CurrentTheme != null)
            {
                themeService.ApplyTheme(themeService.CurrentTheme);
            }
        }
    }
}
