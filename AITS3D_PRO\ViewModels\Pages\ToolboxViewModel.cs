﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace AITS3D_PRO.ViewModels.Pages
{
    public class ToolboxPageViewModel : INotifyPropertyChanged
    {
        public ObservableCollection<string> Blocks { get; set; } = new(); // Initialize with an empty collection to avoid null reference exceptions

        private string _selectedBlock = string.Empty; // Initialize with an empty string as the default value
        public string SelectedBlock
        {
            get => _selectedBlock;
            set
            {
                _selectedBlock = value;
                OnPropertyChanged(); // Call OnPropertyChanged without a parameter to notify all properties have changed
            }
        }

        public ToolboxPageViewModel()
        {
            Blocks.Add("Read Image"); // Add blocks to the collection as needed
            Blocks.Add("Camera");
            Blocks.Add("Crop");
            Blocks.Add("Filter");
            Blocks.Add("Inference");
        }

        public event PropertyChangedEventHandler? PropertyChanged; // Mark as nullable
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null) // Make the parameter nullable to handle null values
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}