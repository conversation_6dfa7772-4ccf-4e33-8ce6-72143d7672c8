{"format": 1, "restore": {"E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\AITS3D_PRO.csproj": {}}, "projects": {"E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\AITS3D_PRO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\AITS3D_PRO.csproj", "projectName": "AITS3D_PRO", "projectPath": "E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\AITS3D_PRO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\flowchart\\AITS3D_PRO v3\\AITS3D_PRO\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Extended.Wpf.Toolkit": {"target": "Package", "version": "[5.0.0, )"}, "MahApps.Metro.IconPacks": {"target": "Package", "version": "[6.1.0, )"}, "MaterialDesignColors": {"target": "Package", "version": "[5.2.1, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "MaterialDesignThemes.MahApps": {"target": "Package", "version": "[5.2.1, )"}, "NodeNetwork": {"target": "Package", "version": "[6.0.0, )"}, "StringMath": {"target": "Package", "version": "[4.1.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}